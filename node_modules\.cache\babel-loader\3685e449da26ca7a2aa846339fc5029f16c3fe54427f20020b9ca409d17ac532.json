{"ast": null, "code": "import { InternSet } from \"internmap\";\nexport default function union(...others) {\n  const set = new InternSet();\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n  return set;\n}", "map": {"version": 3, "names": ["InternSet", "union", "others", "set", "other", "o", "add"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/node_modules/d3-array/src/union.js"], "sourcesContent": ["import {InternSet} from \"internmap\";\n\nexport default function union(...others) {\n  const set = new InternSet();\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n  return set;\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,WAAW;AAEnC,eAAe,SAASC,KAAKA,CAAC,GAAGC,MAAM,EAAE;EACvC,MAAMC,GAAG,GAAG,IAAIH,SAAS,CAAC,CAAC;EAC3B,KAAK,MAAMI,KAAK,IAAIF,MAAM,EAAE;IAC1B,KAAK,MAAMG,CAAC,IAAID,KAAK,EAAE;MACrBD,GAAG,CAACG,GAAG,CAACD,CAAC,CAAC;IACZ;EACF;EACA,OAAOF,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}