import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import { AlertTriangle, Thermometer, Zap, Server, Shield, TrendingUp, Settings, Bell, Search, User, Menu, Activity, Cpu, HardDrive, Wifi } from 'lucide-react';

// Glassmorphism CSS styles
const glassmorphismStyles = `
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600&display=swap');

  .glass-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .glass-card {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .glass-card:hover {
    background: rgba(255, 255, 255, 0.12);
    border: 1px solid rgba(255, 255, 255, 0.25);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  .glass-button {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
  }

  .glass-button:hover {
    background: rgba(255, 255, 255, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
  }

  .glass-text-primary {
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .glass-text-secondary {
    color: rgba(255, 255, 255, 0.7);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  .glass-text-muted {
    color: rgba(255, 255, 255, 0.5);
  }

  .glass-gradient {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  }

  .glass-border {
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-shadow {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  .glass-backdrop {
    background: linear-gradient(135deg, 
      rgba(100, 116, 139, 0.4) 0%, 
      rgba(71, 85, 105, 0.4) 25%, 
      rgba(51, 65, 85, 0.4) 50%, 
      rgba(30, 41, 59, 0.4) 75%, 
      rgba(15, 23, 42, 0.4) 100%);
    backdrop-filter: blur(40px);
    -webkit-backdrop-filter: blur(40px);
  }

  .glass-metric-card {
    background: rgba(255, 255, 255, 0.06);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
  }

  .glass-metric-card:hover {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-thermal-cell {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
  }

  .glass-thermal-cell:hover {
    border: 1px solid rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }

  .glass-alarm-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-left: 3px solid;
    transition: all 0.3s ease;
  }

  .glass-alarm-critical {
    border-left-color: rgba(239, 68, 68, 0.8);
    background: rgba(239, 68, 68, 0.05);
  }

  .glass-alarm-warning {
    border-left-color: rgba(245, 158, 11, 0.8);
    background: rgba(245, 158, 11, 0.05);
  }

  .glass-alarm-info {
    border-left-color: rgba(59, 130, 246, 0.8);
    background: rgba(59, 130, 246, 0.05);
  }

  .glass-fullscreen-button {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
  }

  .glass-fullscreen-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .glass-chart-tooltip {
    background: rgba(0, 0, 0, 0.8) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important;
    color: rgba(255, 255, 255, 0.9) !important;
  }

  .glass-progress-bar {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
  }

  .glass-progress-fill {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.8) 0%, rgba(147, 197, 253, 0.8) 100%);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
  }
`;

// Inject glassmorphism styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = glassmorphismStyles;
  document.head.appendChild(styleElement);
}

const GlassmorphismDataCenterDashboard = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [selectedRack, setSelectedRack] = useState(null);
  const [fullScreenSection, setFullScreenSection] = useState<string | null>(null);
  const [kpiData, setKpiData] = useState({
    pue: 1.45,
    itLoad: 1.1,
    coolingLoad: 1.1,
    trialPower: 2.4,
    upsPower: 1.11,
    pduPower: 1.5,
    totalAlarms: 4,
    activeAlarms: 2
  });
  const [alarms, setAlarms] = useState([]);
  const [heatmapData, setHeatmapData] = useState([]);
  const [anomalies, setAnomalies] = useState([]);
  const [timeSeriesData, setTimeSeriesData] = useState([]);

  // Generate realistic KPI data
  const generateKPIData = () => {
    const now = new Date();
    const hour = now.getHours();
    
    // Simulate daily patterns
    const baseLoad = 0.8 + (Math.sin((hour - 6) * Math.PI / 12) * 0.3);
    const variation = () => (Math.random() - 0.5) * 0.1;
    
    return {
      pue: Math.max(1.2, Math.min(2.0, 1.4 + variation())),
      itLoad: Math.max(0.5, Math.min(2.0, baseLoad + variation())),
      coolingLoad: Math.max(0.5, Math.min(2.0, baseLoad * 0.9 + variation())),
      trialPower: Math.max(1.0, Math.min(3.0, 2.2 + variation())),
      upsPower: Math.max(0.8, Math.min(1.5, 1.05 + variation())),
      pduPower: Math.max(1.0, Math.min(2.0, 1.4 + variation())),
      totalAlarms: Math.floor(Math.random() * 8) + 2,
      activeAlarms: Math.floor(Math.random() * 4) + 1
    };
  };

  // Generate heatmap data
  const generateHeatmapData = () => {
    const data = [];
    for (let row = 1; row <= 5; row++) {
      for (let col = 1; col <= 10; col++) {
        data.push({
          id: `R${row}C${col}`,
          temperature: 20 + Math.random() * 8,
          power: 60 + Math.random() * 35,
          x: col - 1,
          y: row - 1
        });
      }
    }
    return data;
  };

  // Generate time series data
  const generateTimeSeriesData = () => {
    const data = [];
    const now = new Date();
    for (let i = 23; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 60 * 60 * 1000);
      data.push({
        time: time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
        temperature: 22 + Math.sin(i * 0.5) * 2 + Math.random() * 0.5,
        effectiveness: 85 + Math.sin(i * 0.3) * 5 + Math.random() * 2,
        power: 1.2 + Math.sin(i * 0.4) * 0.3 + Math.random() * 0.1
      });
    }
    return data;
  };

  // Generate alarms
  const generateAlarms = () => {
    const alarmTypes = ['Critical', 'Warning', 'Info'];
    const locations = ['Rack A1', 'UPS Room', 'CRAC Unit 2', 'PDU B3', 'Server Room', 'Cooling Tower'];
    const messages = [
      'Temperature threshold exceeded',
      'Power consumption anomaly detected',
      'Network connectivity issue',
      'Cooling system maintenance required',
      'Backup power activated',
      'Security access logged'
    ];
    
    return Array.from({ length: 8 }, (_, i) => ({
      id: i + 1,
      type: alarmTypes[Math.floor(Math.random() * alarmTypes.length)],
      location: locations[Math.floor(Math.random() * locations.length)],
      message: messages[Math.floor(Math.random() * messages.length)],
      time: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toLocaleTimeString(),
      status: Math.random() > 0.3 ? 'Active' : 'Resolved'
    }));
  };

  // Generate anomalies
  const generateAnomalies = () => {
    const types = ['power', 'temperature', 'network', 'cooling'];
    const descriptions = [
      'Unusual power consumption pattern',
      'Temperature spike detected',
      'Network latency anomaly',
      'Cooling efficiency drop'
    ];
    const locations = ['Sector A', 'Rack B2', 'CRAC-01', 'PDU-03'];
    
    return Array.from({ length: 6 }, (_, i) => ({
      id: i + 1,
      type: types[Math.floor(Math.random() * types.length)],
      description: descriptions[Math.floor(Math.random() * descriptions.length)],
      location: locations[Math.floor(Math.random() * locations.length)],
      confidence: 70 + Math.random() * 25,
      timestamp: new Date(Date.now() - Math.random() * 2 * 60 * 60 * 1000)
    }));
  };

  // Temperature color mapping for glassmorphism theme
  const getTemperatureColor = (temp: number) => {
    if (temp < 22) return 'rgba(34, 197, 94, 0.6)';   // Green with transparency
    if (temp < 24) return 'rgba(132, 204, 22, 0.6)';  // Lime
    if (temp < 26) return 'rgba(234, 179, 8, 0.6)';   // Yellow
    if (temp < 28) return 'rgba(249, 115, 22, 0.6)';  // Orange
    if (temp < 30) return 'rgba(239, 68, 68, 0.6)';   // Red
    return 'rgba(220, 38, 38, 0.6)';                  // Dark red
  };

  const getAlarmColor = (type: string) => {
    switch (type) {
      case 'Critical': return 'glass-alarm-critical';
      case 'Warning': return 'glass-alarm-warning';
      case 'Info': return 'glass-alarm-info';
      default: return 'glass-alarm-info';
    }
  };

  // Full screen toggle function
  const toggleFullScreen = (sectionId: string) => {
    if (fullScreenSection === sectionId) {
      setFullScreenSection(null);
      document.body.style.overflow = 'auto';
    } else {
      setFullScreenSection(sectionId);
      document.body.style.overflow = 'auto';
    }
  };

  // Initialize data on component mount
  useEffect(() => {
    setKpiData(generateKPIData());
    setHeatmapData(generateHeatmapData());
    setTimeSeriesData(generateTimeSeriesData());
    setAlarms(generateAlarms());
    setAnomalies(generateAnomalies());
  }, []);

  // Update data periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
      setKpiData(generateKPIData());
      setTimeSeriesData(prev => {
        const newData = [...prev.slice(1), {
          time: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
          temperature: 22 + Math.sin(Date.now() * 0.001) * 2 + Math.random() * 0.5,
          effectiveness: 85 + Math.sin(Date.now() * 0.0008) * 5 + Math.random() * 2,
          power: 1.2 + Math.sin(Date.now() * 0.0012) * 0.3 + Math.random() * 0.1
        }];
        return newData.length > 24 ? newData : [...prev, newData[newData.length - 1]];
      });
    }, 15000);

    return () => clearInterval(interval);
  }, []);

  // Close full screen on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && fullScreenSection) {
        setFullScreenSection(null);
        document.body.style.overflow = 'auto';
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'auto';
    };
  }, [fullScreenSection]);

  return (
    <div className="min-h-screen glass-backdrop">
      <div className="p-6 space-y-6 relative">
        {/* Header */}
        <div className="glass-container rounded-xl p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="glass-card p-3 rounded-lg">
                <Server className="w-8 h-8 glass-text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold glass-text-primary font-['Inter']">
                  Data Center Command
                </h1>
                <p className="glass-text-secondary font-['Inter']">
                  Unified Infrastructure Management
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="glass-text-secondary font-['JetBrains_Mono'] text-sm">
                {currentTime.toLocaleString()}
              </div>
              <div className="glass-button p-2 rounded-lg cursor-pointer">
                <Bell className="w-5 h-5 glass-text-primary" />
              </div>
              <div className="glass-button p-2 rounded-lg cursor-pointer">
                <User className="w-5 h-5 glass-text-primary" />
              </div>
            </div>
          </div>
        </div>

        {/* Executive Overview - Full Width */}
        <div className="glass-container rounded-xl p-6 relative">
          <button
            className="absolute top-4 right-4 z-10 glass-fullscreen-button p-2 rounded-lg glass-text-primary hover:glass-text-secondary transition-all duration-300"
            onClick={() => toggleFullScreen('executive-overview')}
            title="Toggle Full Screen"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
            </svg>
          </button>

          <h2 className="text-xl font-bold mb-6 glass-text-primary font-['Inter'] tracking-wide">
            EXECUTIVE OVERVIEW
          </h2>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* PUE Metric */}
            <div className="glass-metric-card rounded-xl p-6 relative">
              <div className="flex items-center justify-between mb-4">
                <div className="glass-card p-2 rounded-lg">
                  <TrendingUp className="w-5 h-5 glass-text-primary" />
                </div>
                <span className="text-xs glass-text-muted font-['JetBrains_Mono']">PUE</span>
              </div>
              <div className="text-3xl font-bold glass-text-primary font-['JetBrains_Mono'] mb-2">
                {kpiData.pue.toFixed(2)}
              </div>
              <div className="glass-progress-bar h-2 rounded-full overflow-hidden">
                <div
                  className="glass-progress-fill h-full rounded-full transition-all duration-500"
                  style={{ width: `${(kpiData.pue / 3) * 100}%` }}
                ></div>
              </div>
            </div>

            {/* IT Load Metric */}
            <div className="glass-metric-card rounded-xl p-6 relative">
              <div className="flex items-center justify-between mb-4">
                <div className="glass-card p-2 rounded-lg">
                  <Cpu className="w-5 h-5 glass-text-primary" />
                </div>
                <span className="text-xs glass-text-muted font-['JetBrains_Mono']">IT LOAD</span>
              </div>
              <div className="text-3xl font-bold glass-text-primary font-['JetBrains_Mono'] mb-2">
                {kpiData.itLoad.toFixed(1)}M
              </div>
              <div className="glass-progress-bar h-2 rounded-full overflow-hidden">
                <div
                  className="glass-progress-fill h-full rounded-full transition-all duration-500"
                  style={{ width: `${(kpiData.itLoad / 2) * 100}%` }}
                ></div>
              </div>
            </div>

            {/* Cooling Load Metric */}
            <div className="glass-metric-card rounded-xl p-6 relative">
              <div className="flex items-center justify-between mb-4">
                <div className="glass-card p-2 rounded-lg">
                  <Thermometer className="w-5 h-5 glass-text-primary" />
                </div>
                <span className="text-xs glass-text-muted font-['JetBrains_Mono']">COOLING</span>
              </div>
              <div className="text-3xl font-bold glass-text-primary font-['JetBrains_Mono'] mb-2">
                {Math.round(kpiData.coolingLoad * 650)} RT
              </div>
              <div className="glass-progress-bar h-2 rounded-full overflow-hidden">
                <div
                  className="glass-progress-fill h-full rounded-full transition-all duration-500"
                  style={{ width: `${(kpiData.coolingLoad / 2) * 100}%` }}
                ></div>
              </div>
            </div>

            {/* Alarms Metric */}
            <div className="glass-metric-card rounded-xl p-6 relative">
              <div className="flex items-center justify-between mb-4">
                <div className="glass-card p-2 rounded-lg">
                  <AlertTriangle className="w-5 h-5 text-red-400" />
                </div>
                <span className="text-xs glass-text-muted font-['JetBrains_Mono']">ALARMS</span>
              </div>
              <div className="text-3xl font-bold text-red-400 font-['JetBrains_Mono'] mb-2">
                {kpiData.totalAlarms}
              </div>
              <div className="text-sm glass-text-secondary font-['Inter']">
                {kpiData.activeAlarms} Active
              </div>
            </div>
          </div>

          {/* Thermal Effectiveness Chart */}
          <div className="glass-card rounded-xl p-6">
            <h3 className="text-lg font-medium glass-text-primary mb-4 font-['Inter'] tracking-wide">
              Thermal Effectiveness Matrix
            </h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={timeSeriesData.slice(-12)}>
                  <defs>
                    <linearGradient id="glassEffectiveness" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="rgba(59, 130, 246, 0.6)" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="rgba(59, 130, 246, 0.6)" stopOpacity={0.1}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="2 2" stroke="rgba(255, 255, 255, 0.1)" />
                  <XAxis
                    dataKey="time"
                    axisLine={false}
                    tickLine={false}
                    tick={{ fill: 'rgba(255, 255, 255, 0.7)', fontSize: 11, fontFamily: 'Inter' }}
                  />
                  <YAxis
                    domain={[75, 95]}
                    axisLine={false}
                    tickLine={false}
                    tick={{ fill: 'rgba(255, 255, 255, 0.7)', fontSize: 11, fontFamily: 'Inter' }}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0, 0, 0, 0.8)',
                      backdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255, 255, 255, 0.2)',
                      borderRadius: '8px',
                      color: 'rgba(255, 255, 255, 0.9)',
                      fontFamily: 'Inter'
                    }}
                  />
                  <Area
                    type="monotone"
                    dataKey="effectiveness"
                    stroke="rgba(59, 130, 246, 0.8)"
                    strokeWidth={2}
                    fillOpacity={1}
                    fill="url(#glassEffectiveness)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* Asset 360 - Thermal Heatmap - Full Width */}
        <div className="glass-container rounded-xl p-6 relative">
          <button
            className="absolute top-4 right-4 z-10 glass-fullscreen-button p-2 rounded-lg glass-text-primary hover:glass-text-secondary transition-all duration-300"
            onClick={() => toggleFullScreen('thermal-heatmap')}
            title="Toggle Full Screen"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
            </svg>
          </button>

          <h2 className="text-xl font-bold mb-2 glass-text-primary font-['Inter'] tracking-wide">
            ASSET 360 - THERMAL HEATMAP
          </h2>
          <p className="text-sm glass-text-secondary mb-6 font-['Inter']">
            Data Hall Temperature Distribution
          </p>

          {/* Color Legend */}
          <div className="mb-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-xs glass-text-secondary font-['Inter'] tracking-wider uppercase">Temperature Scale:</span>
              <div className="flex items-center space-x-2">
                <span className="text-xs glass-text-primary font-['Inter']">COOL</span>
                <div className="flex space-x-1">
                  <div className="w-4 h-4 rounded glass-border" style={{ backgroundColor: 'rgba(34, 197, 94, 0.6)' }}></div>
                  <div className="w-4 h-4 rounded glass-border" style={{ backgroundColor: 'rgba(132, 204, 22, 0.6)' }}></div>
                  <div className="w-4 h-4 rounded glass-border" style={{ backgroundColor: 'rgba(234, 179, 8, 0.6)' }}></div>
                  <div className="w-4 h-4 rounded glass-border" style={{ backgroundColor: 'rgba(249, 115, 22, 0.6)' }}></div>
                  <div className="w-4 h-4 rounded glass-border" style={{ backgroundColor: 'rgba(239, 68, 68, 0.6)' }}></div>
                  <div className="w-4 h-4 rounded glass-border" style={{ backgroundColor: 'rgba(220, 38, 38, 0.6)' }}></div>
                </div>
                <span className="text-xs glass-text-primary font-['Inter']">HOT</span>
              </div>
            </div>
            <div className="text-xs glass-text-muted font-['Inter']">
              Click rack for details
            </div>
          </div>

          <div className="grid grid-cols-10 gap-1 mb-4">
            {heatmapData.map((rack) => (
              <div
                key={rack.id}
                className="aspect-square rounded-lg cursor-pointer transition-all duration-300 hover:scale-110 relative group glass-thermal-cell"
                style={{
                  backgroundColor: getTemperatureColor(rack.temperature),
                  boxShadow: `0 0 10px ${getTemperatureColor(rack.temperature)}`
                }}
                onClick={() => setSelectedRack(rack)}
              >
                <div className="absolute inset-0 bg-black bg-opacity-10 rounded-lg"></div>
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white bg-opacity-10 rounded-lg"></div>
                {/* Rack Number and Temperature Display */}
                <div className="absolute inset-0 flex flex-col items-center justify-center p-1">
                  <span className="text-xs font-bold glass-text-primary drop-shadow-lg font-['JetBrains_Mono'] leading-tight">
                    {rack.id}
                  </span>
                  <span className="text-xs font-semibold glass-text-primary drop-shadow-lg font-['JetBrains_Mono'] leading-tight">
                    {rack.temperature.toFixed(1)}°C
                  </span>
                </div>
              </div>
            ))}
          </div>

          {selectedRack && (
            <div className="glass-card rounded-xl p-4 relative">
              <h4 className="font-bold glass-text-primary mb-2 font-['Inter']">
                Neural Node: {selectedRack.id}
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="glass-text-secondary font-['Inter']">Temperature:</span>
                  <div className="glass-text-primary font-['JetBrains_Mono'] text-lg font-bold">
                    {selectedRack.temperature.toFixed(1)}°C
                  </div>
                </div>
                <div>
                  <span className="glass-text-secondary font-['Inter']">Power:</span>
                  <div className="glass-text-primary font-['JetBrains_Mono'] text-lg font-bold">
                    {selectedRack.power.toFixed(0)}%
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Recent Alarms Section */}
        <div className="glass-container rounded-xl p-6 relative">
          <button
            className="absolute top-4 right-4 z-10 glass-fullscreen-button p-2 rounded-lg glass-text-primary hover:glass-text-secondary transition-all duration-300"
            onClick={() => toggleFullScreen('recent-alarms')}
            title="Toggle Full Screen"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
            </svg>
          </button>

          <h2 className="text-xl font-bold mb-6 glass-text-primary font-['Inter'] tracking-wide">
            RECENT ALARMS
          </h2>

          <div className="space-y-3">
            {alarms.slice(0, 5).map((alarm) => (
              <div
                key={alarm.id}
                className={`glass-alarm-card rounded-xl p-4 ${getAlarmColor(alarm.type)} hover:glass-card transition-all duration-300`}
              >
                <div className="flex justify-between items-start">
                  <div className="flex items-center space-x-3">
                    <AlertTriangle className={`w-5 h-5 ${
                      alarm.type === 'Critical' ? 'text-red-400' :
                      alarm.type === 'Warning' ? 'text-yellow-400' : 'text-blue-400'
                    }`} />
                    <div>
                      <div className="glass-text-primary font-['Inter'] font-medium">{alarm.message}</div>
                      <div className="text-sm glass-text-secondary font-['Inter']">{alarm.location}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xs glass-text-muted font-['JetBrains_Mono']">{alarm.time}</div>
                    <div className={`text-xs font-medium ${
                      alarm.status === 'Active' ? 'text-red-400' : 'text-green-400'
                    } font-['Inter']`}>
                      {alarm.status}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 text-center">
            <button className="glass-button px-6 py-2 rounded-lg glass-text-primary font-['Inter'] hover:glass-text-secondary transition-all duration-300">
              View All Alarms ({alarms.length})
            </button>
          </div>
        </div>
      </div>

      {/* Full Screen Overlay */}
      {fullScreenSection && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-95 overflow-auto">
          <div className="min-h-screen flex items-start justify-center p-4">
            <div className="w-full max-w-7xl my-4">
              <div className="glass-container rounded-xl p-6 relative min-h-[calc(100vh-2rem)]">
                <button
                  className="absolute top-4 right-4 z-10 glass-fullscreen-button p-3 rounded-lg glass-text-primary hover:glass-text-secondary transition-all duration-300"
                  onClick={() => toggleFullScreen(fullScreenSection)}
                  title="Exit Full Screen"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>

                <div className="text-center glass-text-primary font-['Inter'] text-xl">
                  Full screen view for {fullScreenSection.replace('-', ' ').toUpperCase()} section
                  <div className="mt-4 text-sm glass-text-secondary">
                    Enhanced glassmorphism full-screen functionality
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GlassmorphismDataCenterDashboard;
