{"ast": null, "code": "var castPath = require('./_castPath'),\n  last = require('./last'),\n  parent = require('./_parent'),\n  toKey = require('./_toKey');\n\n/**\n * The base implementation of `_.unset`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The property path to unset.\n * @returns {boolean} Returns `true` if the property is deleted, else `false`.\n */\nfunction baseUnset(object, path) {\n  path = castPath(path, object);\n  object = parent(object, path);\n  return object == null || delete object[toKey(last(path))];\n}\nmodule.exports = baseUnset;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "require", "last", "parent", "to<PERSON><PERSON>", "baseUnset", "object", "path", "module", "exports"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/node_modules/lodash/_baseUnset.js"], "sourcesContent": ["var castPath = require('./_castPath'),\n    last = require('./last'),\n    parent = require('./_parent'),\n    toKey = require('./_toKey');\n\n/**\n * The base implementation of `_.unset`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The property path to unset.\n * @returns {boolean} Returns `true` if the property is deleted, else `false`.\n */\nfunction baseUnset(object, path) {\n  path = castPath(path, object);\n  object = parent(object, path);\n  return object == null || delete object[toKey(last(path))];\n}\n\nmodule.exports = baseUnset;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;EACjCC,IAAI,GAAGD,OAAO,CAAC,QAAQ,CAAC;EACxBE,MAAM,GAAGF,OAAO,CAAC,WAAW,CAAC;EAC7BG,KAAK,GAAGH,OAAO,CAAC,UAAU,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,SAASA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC/BA,IAAI,GAAGP,QAAQ,CAACO,IAAI,EAAED,MAAM,CAAC;EAC7BA,MAAM,GAAGH,MAAM,CAACG,MAAM,EAAEC,IAAI,CAAC;EAC7B,OAAOD,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAACF,KAAK,CAACF,IAAI,CAACK,IAAI,CAAC,CAAC,CAAC;AAC3D;AAEAC,MAAM,CAACC,OAAO,GAAGJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}