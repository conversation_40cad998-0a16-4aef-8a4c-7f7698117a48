{"ast": null, "code": "var mapCacheClear = require('./_mapCacheClear'),\n  mapCacheDelete = require('./_mapCacheDelete'),\n  mapCacheGet = require('./_mapCacheGet'),\n  mapCacheHas = require('./_mapCacheHas'),\n  mapCacheSet = require('./_mapCacheSet');\n\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction MapCache(entries) {\n  var index = -1,\n    length = entries == null ? 0 : entries.length;\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype['delete'] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\nmodule.exports = MapCache;", "map": {"version": 3, "names": ["mapCacheClear", "require", "mapCacheDelete", "mapCacheGet", "mapCacheHas", "mapCacheSet", "MapCache", "entries", "index", "length", "clear", "entry", "set", "prototype", "get", "has", "module", "exports"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/node_modules/lodash/_MapCache.js"], "sourcesContent": ["var mapCacheClear = require('./_mapCacheClear'),\n    mapCacheDelete = require('./_mapCacheDelete'),\n    mapCacheGet = require('./_mapCacheGet'),\n    mapCacheHas = require('./_mapCacheHas'),\n    mapCacheSet = require('./_mapCacheSet');\n\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction MapCache(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype['delete'] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\n\nmodule.exports = MapCache;\n"], "mappings": "AAAA,IAAIA,aAAa,GAAGC,OAAO,CAAC,kBAAkB,CAAC;EAC3CC,cAAc,GAAGD,OAAO,CAAC,mBAAmB,CAAC;EAC7CE,WAAW,GAAGF,OAAO,CAAC,gBAAgB,CAAC;EACvCG,WAAW,GAAGH,OAAO,CAAC,gBAAgB,CAAC;EACvCI,WAAW,GAAGJ,OAAO,CAAC,gBAAgB,CAAC;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,QAAQA,CAACC,OAAO,EAAE;EACzB,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGF,OAAO,IAAI,IAAI,GAAG,CAAC,GAAGA,OAAO,CAACE,MAAM;EAEjD,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,OAAO,EAAEF,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIE,KAAK,GAAGJ,OAAO,CAACC,KAAK,CAAC;IAC1B,IAAI,CAACI,GAAG,CAACD,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9B;AACF;;AAEA;AACAL,QAAQ,CAACO,SAAS,CAACH,KAAK,GAAGV,aAAa;AACxCM,QAAQ,CAACO,SAAS,CAAC,QAAQ,CAAC,GAAGX,cAAc;AAC7CI,QAAQ,CAACO,SAAS,CAACC,GAAG,GAAGX,WAAW;AACpCG,QAAQ,CAACO,SAAS,CAACE,GAAG,GAAGX,WAAW;AACpCE,QAAQ,CAACO,SAAS,CAACD,GAAG,GAAGP,WAAW;AAEpCW,MAAM,CAACC,OAAO,GAAGX,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}