{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst UserCog2 = createLucideIcon(\"UserCog2\", [[\"path\", {\n  d: \"M14 19a6 6 0 0 0-12 0\",\n  key: \"vej9p1\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"9\",\n  r: \"4\",\n  key: \"143rtg\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"11\",\n  r: \"2\",\n  key: \"1rxg02\"\n}], [\"path\", {\n  d: \"M19 8v1\",\n  key: \"1iffrw\"\n}], [\"path\", {\n  d: \"M19 13v1\",\n  key: \"z4xc62\"\n}], [\"path\", {\n  d: \"m21.6 9.5-.87.5\",\n  key: \"6lxupl\"\n}], [\"path\", {\n  d: \"m17.27 12-.87.5\",\n  key: \"1rwhxx\"\n}], [\"path\", {\n  d: \"m21.6 12.5-.87-.5\",\n  key: \"agvc9a\"\n}], [\"path\", {\n  d: \"m17.27 10-.87-.5\",\n  key: \"12d57s\"\n}]]);\nexport { UserCog2 as default };", "map": {"version": 3, "names": ["UserCog2", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["D:\\00-WKYap\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\node_modules\\lucide-react\\src\\icons\\user-cog-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name UserCog2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgMTlhNiA2IDAgMCAwLTEyIDAiIC8+CiAgPGNpcmNsZSBjeD0iOCIgY3k9IjkiIHI9IjQiIC8+CiAgPGNpcmNsZSBjeD0iMTkiIGN5PSIxMSIgcj0iMiIgLz4KICA8cGF0aCBkPSJNMTkgOHYxIiAvPgogIDxwYXRoIGQ9Ik0xOSAxM3YxIiAvPgogIDxwYXRoIGQ9Im0yMS42IDkuNS0uODcuNSIgLz4KICA8cGF0aCBkPSJtMTcuMjcgMTItLjg3LjUiIC8+CiAgPHBhdGggZD0ibTIxLjYgMTIuNS0uODctLjUiIC8+CiAgPHBhdGggZD0ibTE3LjI3IDEwLS44Ny0uNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/user-cog-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UserCog2 = createLucideIcon('UserCog2', [\n  ['path', { d: 'M14 19a6 6 0 0 0-12 0', key: 'vej9p1' }],\n  ['circle', { cx: '8', cy: '9', r: '4', key: '143rtg' }],\n  ['circle', { cx: '19', cy: '11', r: '2', key: '1rxg02' }],\n  ['path', { d: 'M19 8v1', key: '1iffrw' }],\n  ['path', { d: 'M19 13v1', key: 'z4xc62' }],\n  ['path', { d: 'm21.6 9.5-.87.5', key: '6lxupl' }],\n  ['path', { d: 'm17.27 12-.87.5', key: '1rwhxx' }],\n  ['path', { d: 'm21.6 12.5-.87-.5', key: 'agvc9a' }],\n  ['path', { d: 'm17.27 10-.87-.5', key: '12d57s' }],\n]);\n\nexport default UserCog2;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACtD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}