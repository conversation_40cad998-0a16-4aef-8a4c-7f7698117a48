{"ast": null, "code": "var _jsxFileName = \"D:\\\\00-WKYap\\\\12-AIApps-AgumentCode\\\\01-Unified IPS Dashboard\\\\src\\\\components\\\\UnifiedDataCenterDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';\nimport { AlertTriangle, Bell, Search, User, Menu, Cpu } from 'lucide-react';\n\n// Futuristic CSS styles\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst futuristicStyles = `\n  @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap');\n  \n  .futuristic-bg {\n    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0a0a0a 100%);\n    min-height: 100vh;\n    position: relative;\n    overflow-x: hidden;\n  }\n  \n  .futuristic-bg::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: \n      radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),\n      radial-gradient(circle at 40% 60%, rgba(0, 255, 0, 0.05) 0%, transparent 50%);\n    pointer-events: none;\n  }\n  \n  .neon-border {\n    border: 1px solid rgba(0, 255, 255, 0.3);\n    box-shadow: \n      0 0 10px rgba(0, 255, 255, 0.2),\n      inset 0 0 10px rgba(0, 255, 255, 0.1);\n    background: rgba(0, 20, 40, 0.8);\n    backdrop-filter: blur(10px);\n  }\n  \n  .neon-glow {\n    box-shadow: \n      0 0 20px rgba(0, 255, 255, 0.4),\n      0 0 40px rgba(0, 255, 255, 0.2),\n      inset 0 0 20px rgba(0, 255, 255, 0.1);\n  }\n  \n  .hologram-text {\n    font-family: 'Orbitron', monospace;\n    color: #00ffff;\n    text-shadow: \n      0 0 10px rgba(0, 255, 255, 0.8),\n      0 0 20px rgba(0, 255, 255, 0.4),\n      0 0 30px rgba(0, 255, 255, 0.2);\n  }\n  \n  .data-stream {\n    background: linear-gradient(90deg, transparent 0%, rgba(0, 255, 255, 0.1) 50%, transparent 100%);\n    animation: dataFlow 3s linear infinite;\n  }\n  \n  @keyframes dataFlow {\n    0% { transform: translateX(-100%); }\n    100% { transform: translateX(100%); }\n  }\n  \n  .pulse-glow {\n    animation: pulseGlow 2s ease-in-out infinite alternate;\n  }\n  \n  @keyframes pulseGlow {\n    from { box-shadow: 0 0 20px rgba(0, 255, 255, 0.4); }\n    to { box-shadow: 0 0 30px rgba(0, 255, 255, 0.8), 0 0 40px rgba(0, 255, 255, 0.4); }\n  }\n  \n  .matrix-bg {\n    background: \n      linear-gradient(90deg, transparent 98%, rgba(0, 255, 255, 0.1) 100%),\n      linear-gradient(0deg, transparent 98%, rgba(0, 255, 255, 0.1) 100%);\n    background-size: 20px 20px;\n  }\n  \n  .scan-line {\n    position: relative;\n    overflow: hidden;\n  }\n  \n  .scan-line::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 2px;\n    background: linear-gradient(90deg, transparent, #00ffff, transparent);\n    animation: scanLine 3s linear infinite;\n  }\n  \n  @keyframes scanLine {\n    0% { left: -100%; }\n    100% { left: 100%; }\n  }\n  \n  .custom-scrollbar::-webkit-scrollbar {\n    width: 8px;\n  }\n  \n  .custom-scrollbar::-webkit-scrollbar-track {\n    background: rgba(0, 20, 40, 0.5);\n    border-radius: 4px;\n  }\n  \n  .custom-scrollbar::-webkit-scrollbar-thumb {\n    background: linear-gradient(180deg, #00ffff, #0080ff);\n    border-radius: 4px;\n    box-shadow: 0 0 10px rgba(0, 255, 255, 0.4);\n  }\n  \n  .custom-scrollbar::-webkit-scrollbar-thumb:hover {\n    background: linear-gradient(180deg, #00ffff, #00aaff);\n    box-shadow: 0 0 15px rgba(0, 255, 255, 0.6);\n  }\n  \n  /* Responsive adjustments */\n  @media (max-width: 768px) {\n    .hologram-text {\n      font-size: 0.9em;\n    }\n    \n    .neon-border {\n      padding: 1rem;\n    }\n  }\n`;\n\n// Inject styles\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style');\n  styleElement.textContent = futuristicStyles;\n  document.head.appendChild(styleElement);\n}\nconst UnifiedDataCenterDashboard = () => {\n  _s();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [selectedRack, setSelectedRack] = useState(null);\n  const [kpiData, setKpiData] = useState({\n    pue: 1.45,\n    itLoad: 1.1,\n    coolingLoad: 1.1,\n    trialPower: 2.4,\n    upsPower: 1.11,\n    pduPower: 1.5,\n    totalAlarms: 4,\n    activeAlarms: 2\n  });\n  const [alarms, setAlarms] = useState([]);\n  const [heatmapData, setHeatmapData] = useState([]);\n  const [anomalies, setAnomalies] = useState([]);\n  const [timeSeriesData, setTimeSeriesData] = useState([]);\n\n  // Generate realistic KPI data\n  const generateKPIData = () => {\n    const now = new Date();\n    const hour = now.getHours();\n    const minute = now.getMinutes();\n    const timeVariation = Math.sin((hour * 60 + minute) * Math.PI / 720);\n    return {\n      pue: 1.42 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.02,\n      itLoad: 1.08 + timeVariation * 0.15 + (Math.random() - 0.5) * 0.03,\n      coolingLoad: 1.1 + timeVariation * 0.08 + (Math.random() - 0.5) * 0.02,\n      trialPower: 2.35 + timeVariation * 0.2 + (Math.random() - 0.5) * 0.05,\n      upsPower: 1.1 + (Math.random() - 0.5) * 0.01,\n      pduPower: 1.48 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.02,\n      totalAlarms: Math.floor(3 + Math.random() * 5),\n      activeAlarms: Math.floor(1 + Math.random() * 3)\n    };\n  };\n\n  // Generate alarm data\n  const generateAlarmData = () => {\n    const alarmTypes = ['Critical', 'Warning', 'Info'];\n    const locations = ['Rack A1', 'Rack B3', 'UPS Room', 'Cooling Unit 2', 'Switch Core', 'PDU Zone C'];\n    const messages = {\n      Critical: ['Temperature Threshold Exceeded', 'Power Supply Failure', 'Cooling System Down'],\n      Warning: ['High Humidity Detected', 'UPS Battery Low', 'Network Latency High'],\n      Info: ['Scheduled Maintenance', 'System Update Complete', 'Backup Process Started']\n    };\n    const numAlarms = 3 + Math.floor(Math.random() * 4);\n    const alarms = [];\n    for (let i = 0; i < numAlarms; i++) {\n      const type = alarmTypes[Math.floor(Math.random() * alarmTypes.length)];\n      const location = locations[Math.floor(Math.random() * locations.length)];\n      const messageList = messages[type];\n      const message = messageList[Math.floor(Math.random() * messageList.length)];\n      const time = new Date(Date.now() - Math.random() * 3600000).toLocaleTimeString();\n      alarms.push({\n        id: i + 1,\n        type,\n        message,\n        location,\n        time,\n        status: Math.random() > 0.5 ? 'Active' : 'Acknowledged'\n      });\n    }\n    return alarms.sort((a, b) => {\n      const priority = {\n        Critical: 3,\n        Warning: 2,\n        Info: 1\n      };\n      return priority[b.type] - priority[a.type];\n    });\n  };\n\n  // Generate heatmap data\n  const generateHeatmapData = () => {\n    const data = [];\n    const baseTemp = 22;\n    const now = Date.now();\n    for (let row = 0; row < 8; row++) {\n      for (let col = 0; col < 10; col++) {\n        const distanceFromCenter = Math.sqrt(Math.pow(col - 4.5, 2) + Math.pow(row - 3.5, 2));\n        const hotSpotInfluence = Math.sin(now / 10000 + row * col) * 2;\n        const randomVariation = (Math.random() - 0.5) * 3;\n        const temperature = baseTemp + distanceFromCenter * 0.8 + hotSpotInfluence + randomVariation;\n        const power = 60 + Math.random() * 30 + Math.sin(now / 8000 + row + col) * 10;\n        data.push({\n          x: col,\n          y: row,\n          temperature: Math.max(18, Math.min(32, temperature)),\n          power: Math.max(20, Math.min(100, power)),\n          id: `R${row + 1}C${col + 1}`,\n          status: temperature > 28 ? 'critical' : temperature > 25 ? 'warning' : 'normal'\n        });\n      }\n    }\n    return data;\n  };\n\n  // Generate anomaly data\n  const generateAnomalies = () => {\n    const anomalyTypes = [{\n      icon: 'power',\n      description: 'Unusual power consumption pattern'\n    }, {\n      icon: 'temperature',\n      description: 'Temperature gradient anomaly'\n    }, {\n      icon: 'network',\n      description: 'Network bandwidth surge detected'\n    }, {\n      icon: 'cooling',\n      description: 'HVAC performance degradation'\n    }];\n    const locations = ['Rack A3-A5', 'Zone B, Rows 3-4', 'Core switches', 'Cooling Unit 1'];\n    const numAnomalies = 2 + Math.floor(Math.random() * 3);\n    const anomalies = [];\n    for (let i = 0; i < numAnomalies; i++) {\n      const anomaly = anomalyTypes[Math.floor(Math.random() * anomalyTypes.length)];\n      const location = locations[Math.floor(Math.random() * locations.length)];\n      const confidence = 75 + Math.floor(Math.random() * 25);\n      anomalies.push({\n        id: i + 1,\n        ...anomaly,\n        location,\n        confidence,\n        severity: confidence > 90 ? 'critical' : confidence > 80 ? 'warning' : 'info'\n      });\n    }\n    return anomalies.sort((a, b) => b.confidence - a.confidence);\n  };\n\n  // Generate time series data\n  const generateTimeSeriesData = () => {\n    const data = [];\n    const now = new Date();\n    for (let i = 23; i >= 0; i--) {\n      const time = new Date(now.getTime() - i * 60 * 60 * 1000);\n      const hour = time.getHours();\n      const timeVariation = Math.sin(hour * Math.PI / 12);\n      data.push({\n        time: time.toLocaleTimeString('en-US', {\n          hour: '2-digit',\n          minute: '2-digit'\n        }),\n        power: 1.4 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.05,\n        cooling: 1.1 + timeVariation * 0.08 + (Math.random() - 0.5) * 0.03,\n        temperature: 23 + timeVariation * 1.5 + (Math.random() - 0.5) * 0.8,\n        effectiveness: 85 + timeVariation * 5 + (Math.random() - 0.5) * 3\n      });\n    }\n    return data;\n  };\n\n  // Initialize data on component mount\n  useEffect(() => {\n    setKpiData(generateKPIData());\n    setAlarms(generateAlarmData());\n    setHeatmapData(generateHeatmapData());\n    setAnomalies(generateAnomalies());\n    setTimeSeriesData(generateTimeSeriesData());\n  }, []);\n\n  // Update time and KPI data every second\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n      setKpiData(generateKPIData());\n      setTimeSeriesData(generateTimeSeriesData());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Update heatmap every 3 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setHeatmapData(generateHeatmapData());\n    }, 3000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Update alarms every 15 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setAlarms(generateAlarmData());\n    }, 15000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Update anomalies every 20 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setAnomalies(generateAnomalies());\n    }, 20000);\n    return () => clearInterval(interval);\n  }, []);\n  const getTemperatureColor = temp => {\n    if (temp < 20) return '#4ade80';\n    if (temp < 22) return '#84cc16';\n    if (temp < 24) return '#eab308';\n    if (temp < 26) return '#f97316';\n    if (temp < 28) return '#ef4444';\n    return '#dc2626';\n  };\n  const getAlarmColor = type => {\n    switch (type) {\n      case 'Critical':\n        return 'bg-red-50 text-red-700 border-red-200';\n      case 'Warning':\n        return 'bg-yellow-50 text-yellow-700 border-yellow-200';\n      case 'Info':\n        return 'bg-blue-50 text-blue-700 border-blue-200';\n      default:\n        return 'text-gray-600 bg-gray-50';\n    }\n  };\n  const KPIGauge = ({\n    title,\n    value,\n    unit,\n    max,\n    color = 'blue'\n  }) => {\n    const percentage = value / max * 100;\n    const strokeColor = color === 'green' ? '#00ff88' : color === 'red' ? '#ff0044' : '#00ffff';\n    const glowColor = color === 'green' ? 'rgba(0, 255, 136, 0.4)' : color === 'red' ? 'rgba(255, 0, 68, 0.4)' : 'rgba(0, 255, 255, 0.4)';\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"neon-border p-6 rounded-lg relative overflow-hidden matrix-bg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"data-stream absolute top-0 left-0 w-full h-1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-3xl font-bold hologram-text font-['Orbitron']\",\n          children: [value, /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg text-cyan-300\",\n            children: unit\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 20\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-20 h-20 relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-20 h-20 transform -rotate-90\",\n            viewBox: \"0 0 36 36\",\n            children: [/*#__PURE__*/_jsxDEV(\"defs\", {\n              children: /*#__PURE__*/_jsxDEV(\"filter\", {\n                id: `glow-${title}`,\n                children: [/*#__PURE__*/_jsxDEV(\"feGaussianBlur\", {\n                  stdDeviation: \"3\",\n                  result: \"coloredBlur\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"feMerge\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"feMergeNode\", {\n                    in: \"coloredBlur\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"feMergeNode\", {\n                    in: \"SourceGraphic\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\",\n              fill: \"none\",\n              stroke: \"rgba(0, 255, 255, 0.2)\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\",\n              fill: \"none\",\n              stroke: strokeColor,\n              strokeWidth: \"3\",\n              strokeDasharray: `${percentage}, 100`,\n              strokeLinecap: \"round\",\n              style: {\n                filter: `drop-shadow(0 0 10px ${glowColor})`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-bold hologram-text font-['Orbitron']\",\n              children: [Math.round(percentage), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 text-xs text-cyan-500 font-['Exo_2']\",\n        children: [\"MAX: \", max, unit]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"futuristic-bg min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"neon-border backdrop-blur-md px-6 py-4 relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scan-line\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(Menu, {\n              className: \"w-8 h-8 text-cyan-400 pulse-glow cursor-pointer hover:text-cyan-300 transition-colors\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 w-8 h-8 border border-cyan-400 rounded opacity-30 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold hologram-text font-['Orbitron']\",\n              children: \"UNIFIED DATA CENTER\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-cyan-300 font-['Exo_2'] tracking-wider\",\n              children: \"NEURAL COMMAND INTERFACE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative neon-border rounded-lg px-4 py-2\",\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              className: \"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-cyan-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search neural networks...\",\n              className: \"pl-10 pr-4 py-1 bg-transparent text-cyan-300 placeholder-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-400 font-['Exo_2']\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-cyan-300 font-['Exo_2'] font-mono\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-cyan-500\",\n              children: \"SYSTEM TIME\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hologram-text\",\n              children: currentTime.toLocaleTimeString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(Bell, {\n                className: \"w-6 h-6 text-cyan-400 hover:text-cyan-300 transition-colors cursor-pointer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(User, {\n                className: \"w-6 h-6 text-cyan-400 hover:text-cyan-300 transition-colors cursor-pointer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 w-6 h-6 border border-cyan-400 rounded-full animate-ping opacity-20\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 space-y-6 relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"neon-border rounded-lg p-6 relative overflow-hidden matrix-bg\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"absolute top-4 right-4 z-10 neon-border p-2 rounded-lg text-cyan-400 hover:text-cyan-300 hover:neon-glow transition-all duration-300\",\n          onClick: () => toggleFullScreen('executive-overview'),\n          title: \"Toggle Full Screen\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"scan-line\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold mb-6 hologram-text font-['Orbitron'] tracking-wider\",\n          children: \"EXECUTIVE OVERVIEW\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(KPIGauge, {\n            title: \"PUE\",\n            value: kpiData.pue.toFixed(2),\n            unit: \"\",\n            max: 3,\n            color: \"green\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(KPIGauge, {\n            title: \"IT Load\",\n            value: kpiData.itLoad.toFixed(1),\n            unit: \"M\",\n            max: 2,\n            color: \"blue\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(KPIGauge, {\n            title: \"Cooling Load\",\n            value: Math.round(kpiData.coolingLoad * 650),\n            unit: \" RT\",\n            max: 1000,\n            color: \"blue\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"neon-border p-4 rounded-lg relative overflow-hidden matrix-bg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"data-stream absolute top-0 left-0 w-full h-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-cyan-400 mb-2 font-['Exo_2'] tracking-wider uppercase\",\n              children: \"Alarms\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-red-400 hologram-text font-['Orbitron']\",\n              children: kpiData.totalAlarms\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-cyan-500 font-['Exo_2']\",\n              children: [kpiData.activeAlarms, \" Active\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-2 right-2\",\n              children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                className: \"w-5 h-5 text-red-400 animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase\",\n            children: \"Thermal Effectiveness Matrix\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-40 neon-border rounded-lg p-4 relative overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"data-stream absolute top-0 left-0 w-full h-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: \"100%\",\n              children: /*#__PURE__*/_jsxDEV(AreaChart, {\n                data: timeSeriesData.slice(-12),\n                children: [/*#__PURE__*/_jsxDEV(\"defs\", {\n                  children: /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n                    id: \"colorEffectiveness\",\n                    x1: \"0\",\n                    y1: \"0\",\n                    x2: \"0\",\n                    y2: \"1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n                      offset: \"5%\",\n                      stopColor: \"#00ffff\",\n                      stopOpacity: 0.8\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 501,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n                      offset: \"95%\",\n                      stopColor: \"#00ffff\",\n                      stopOpacity: 0.1\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 502,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 500,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"2 2\",\n                  stroke: \"rgba(0, 255, 255, 0.2)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"time\",\n                  axisLine: false,\n                  tickLine: false,\n                  tick: {\n                    fill: '#00ffff',\n                    fontSize: 10,\n                    fontFamily: 'Exo 2'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                  domain: [75, 95],\n                  axisLine: false,\n                  tickLine: false,\n                  tick: {\n                    fill: '#00ffff',\n                    fontSize: 10,\n                    fontFamily: 'Exo 2'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  contentStyle: {\n                    backgroundColor: 'rgba(0, 20, 40, 0.9)',\n                    border: '1px solid rgba(0, 255, 255, 0.3)',\n                    borderRadius: '8px',\n                    color: '#00ffff',\n                    fontFamily: 'Exo 2'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Area, {\n                  type: \"monotone\",\n                  dataKey: \"effectiveness\",\n                  stroke: \"#00ffff\",\n                  strokeWidth: 2,\n                  fillOpacity: 1,\n                  fill: \"url(#colorEffectiveness)\",\n                  style: {\n                    filter: 'drop-shadow(0 0 10px rgba(0, 255, 255, 0.4))'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"neon-border rounded-lg p-6 relative overflow-hidden matrix-bg\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"absolute top-4 right-4 z-10 neon-border p-2 rounded-lg text-cyan-400 hover:text-cyan-300 hover:neon-glow transition-all duration-300\",\n          onClick: () => toggleFullScreen('thermal-heatmap'),\n          title: \"Toggle Full Screen\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"scan-line\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold mb-2 hologram-text font-['Orbitron'] tracking-wider\",\n          children: \"ASSET 360 - THERMAL HEATMAP\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-cyan-400 mb-6 font-['Exo_2'] tracking-wider\",\n          children: \"Data Hall Temperature Distribution\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-cyan-400 font-['Exo_2'] tracking-wider uppercase\",\n              children: \"Temperature Scale:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-cyan-300 font-['Exo_2']\",\n                children: \"COOL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-4 h-4 rounded\",\n                  style: {\n                    backgroundColor: '#4ade80'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-4 h-4 rounded\",\n                  style: {\n                    backgroundColor: '#84cc16'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-4 h-4 rounded\",\n                  style: {\n                    backgroundColor: '#eab308'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-4 h-4 rounded\",\n                  style: {\n                    backgroundColor: '#f97316'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-4 h-4 rounded\",\n                  style: {\n                    backgroundColor: '#ef4444'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-4 h-4 rounded\",\n                  style: {\n                    backgroundColor: '#dc2626'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-cyan-300 font-['Exo_2']\",\n                children: \"HOT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-cyan-500 font-['Exo_2']\",\n            children: \"Click rack for details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-10 gap-1 mb-4\",\n          children: heatmapData.map(rack => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"aspect-square rounded cursor-pointer transition-all duration-300 hover:scale-110 relative group\",\n            style: {\n              backgroundColor: getTemperatureColor(rack.temperature),\n              boxShadow: `0 0 10px ${getTemperatureColor(rack.temperature)}40`\n            },\n            onClick: () => setSelectedRack(rack),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-black bg-opacity-20 rounded\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-cyan-400 bg-opacity-20 rounded\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 flex flex-col items-center justify-center p-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs font-bold text-white drop-shadow-lg font-['Orbitron'] leading-tight\",\n                children: rack.id\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs font-semibold text-white drop-shadow-lg font-['Exo_2'] leading-tight\",\n                children: [rack.temperature.toFixed(1), \"\\xB0C\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 17\n            }, this)]\n          }, rack.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 11\n        }, this), selectedRack && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"neon-border p-4 rounded-lg relative overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"data-stream absolute top-0 left-0 w-full h-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-bold text-cyan-400 mb-2 font-['Orbitron']\",\n            children: [\"Neural Node: \", selectedRack.id]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-cyan-500 font-['Exo_2']\",\n                children: \"Temperature:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hologram-text font-['Orbitron']\",\n                children: [selectedRack.temperature.toFixed(1), \"\\xB0C\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-cyan-500 font-['Exo_2']\",\n                children: \"Power:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hologram-text font-['Orbitron']\",\n                children: [selectedRack.power.toFixed(0), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"neon-border rounded-lg p-6 relative overflow-hidden matrix-bg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"scan-line\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 635,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold mb-6 hologram-text font-['Orbitron'] tracking-wider\",\n          children: \"RECENT ALARMS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: alarms.slice(0, 5).map(alarm => {\n            const getAlarmIcon = () => {\n              switch (alarm.type) {\n                case 'Critical':\n                  return /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                    className: \"w-5 h-5 text-red-400 animate-pulse\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 645,\n                    columnNumber: 28\n                  }, this);\n                case 'Warning':\n                  return /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                    className: \"w-5 h-5 text-yellow-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 647,\n                    columnNumber: 28\n                  }, this);\n                default:\n                  return /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                    className: \"w-5 h-5 text-blue-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 649,\n                    columnNumber: 28\n                  }, this);\n              }\n            };\n            const getAlarmBorderColor = () => {\n              switch (alarm.type) {\n                case 'Critical':\n                  return 'border-red-400';\n                case 'Warning':\n                  return 'border-yellow-400';\n                default:\n                  return 'border-blue-400';\n              }\n            };\n            const getAlarmTextColor = () => {\n              switch (alarm.type) {\n                case 'Critical':\n                  return 'text-red-400';\n                case 'Warning':\n                  return 'text-yellow-400';\n                default:\n                  return 'text-blue-400';\n              }\n            };\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `neon-border ${getAlarmBorderColor()} p-4 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"data-stream absolute top-0 left-0 w-full h-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [getAlarmIcon(), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-3 mb-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `text-sm font-bold ${getAlarmTextColor()} font-['Orbitron'] tracking-wider`,\n                        children: alarm.type.toUpperCase()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 686,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-cyan-500 font-['Exo_2']\",\n                        children: alarm.location\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 689,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 685,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-cyan-300 font-['Exo_2']\",\n                      children: alarm.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 693,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 684,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 682,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-cyan-400 font-['Exo_2'] mb-1\",\n                    children: alarm.time\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 699,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-xs font-bold ${alarm.status === 'Active' ? 'text-red-400' : 'text-green-400'} font-['Orbitron']`,\n                    children: alarm.status.toUpperCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 702,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 19\n              }, this)]\n            }, alarm.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 640,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"neon-border px-6 py-2 rounded-lg text-cyan-400 hover:text-cyan-300 hover:neon-glow transition-all duration-300 font-['Exo_2'] tracking-wider uppercase text-sm\",\n            children: [\"View All Alarms (\", alarms.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 634,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"neon-border rounded-lg p-6 relative overflow-hidden matrix-bg\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"absolute top-4 right-4 z-10 neon-border p-2 rounded-lg text-cyan-400 hover:text-cyan-300 hover:neon-glow transition-all duration-300\",\n          onClick: () => toggleFullScreen('predictive-ai'),\n          title: \"Toggle Full Screen\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 727,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 722,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"scan-line\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 731,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold mb-6 hologram-text font-['Orbitron'] tracking-wider\",\n          children: \"PREDICTIVE AI INSIGHTS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 732,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"neon-border rounded-lg p-6 relative overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"absolute top-4 right-4 z-10 neon-border p-2 rounded-lg text-cyan-400 hover:text-cyan-300 hover:neon-glow transition-all duration-300\",\n              onClick: () => toggleFullScreen('cooling-forecast'),\n              title: \"Toggle Full Screen\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 745,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 739,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"data-stream absolute top-0 left-0 w-full h-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-cyan-400 mb-4 font-['Exo_2'] tracking-wider uppercase\",\n              children: \"Cooling Forecast\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 749,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-64\",\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(LineChart, {\n                  data: timeSeriesData.slice(-12),\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"2 2\",\n                    stroke: \"rgba(0, 255, 255, 0.2)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 755,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"time\",\n                    axisLine: false,\n                    tickLine: false,\n                    tick: {\n                      fill: '#00ffff',\n                      fontSize: 11,\n                      fontFamily: 'Exo 2'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 756,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                    domain: [20, 26],\n                    axisLine: false,\n                    tickLine: false,\n                    tick: {\n                      fill: '#00ffff',\n                      fontSize: 11,\n                      fontFamily: 'Exo 2'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 762,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    contentStyle: {\n                      backgroundColor: 'rgba(0, 20, 40, 0.9)',\n                      border: '1px solid rgba(0, 255, 255, 0.3)',\n                      borderRadius: '8px',\n                      color: '#00ffff',\n                      fontFamily: 'Exo 2'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 768,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Line, {\n                    type: \"monotone\",\n                    dataKey: \"temperature\",\n                    stroke: \"#f59e0b\",\n                    strokeWidth: 3,\n                    dot: false,\n                    style: {\n                      filter: 'drop-shadow(0 0 10px rgba(245, 158, 11, 0.6))'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 777,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 754,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"neon-border rounded-lg p-6 relative overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"absolute top-4 right-4 z-10 neon-border p-2 rounded-lg text-cyan-400 hover:text-cyan-300 hover:neon-glow transition-all duration-300\",\n              onClick: () => toggleFullScreen('recommendations'),\n              title: \"Toggle Full Screen\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 798,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 797,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 792,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"data-stream absolute top-0 left-0 w-full h-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-cyan-400 mb-4 font-['Exo_2'] tracking-wider uppercase\",\n              children: \"Recommendations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"neon-border p-4 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"data-stream absolute top-0 left-0 w-full h-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 807,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-cyan-300 font-['Exo_2'] text-sm\",\n                    children: \"Optimize I/O airflows Sector A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 809,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-green-400 font-['Orbitron'] font-bold text-sm\",\n                    children: \"EXEC A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 810,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 808,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"neon-border p-4 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"data-stream absolute top-0 left-0 w-full h-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-cyan-300 font-['Exo_2'] text-sm\",\n                    children: \"Monitor return chilled water\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 816,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-yellow-400 font-['Orbitron'] font-bold text-sm\",\n                    children: \"CHECK B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 817,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 815,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"neon-border p-4 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"data-stream absolute top-0 left-0 w-full h-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-cyan-300 font-['Exo_2'] text-sm\",\n                    children: \"Server space cooling pump\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 823,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-blue-400 font-['Orbitron'] font-bold text-sm\",\n                    children: \"CHECK A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 824,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 805,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 791,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"neon-border rounded-lg p-4 relative overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"data-stream absolute top-0 left-0 w-full h-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 832,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase\",\n              children: \"Anomaly Detection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 833,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2 h-48 overflow-y-auto\",\n              children: anomalies.slice(0, 4).map(anomaly => {\n                const getConfidenceColor = confidence => {\n                  if (confidence >= 90) return 'text-red-400';\n                  if (confidence >= 70) return 'text-yellow-400';\n                  return 'text-green-400';\n                };\n                const getConfidenceBorder = confidence => {\n                  if (confidence >= 90) return 'border-red-400';\n                  if (confidence >= 70) return 'border-yellow-400';\n                  return 'border-green-400';\n                };\n                const getIcon = type => {\n                  switch (type) {\n                    case 'power':\n                      return '⚡';\n                    case 'network':\n                      return '🌐';\n                    case 'temperature':\n                      return '🌡️';\n                    default:\n                      return '⚠️';\n                  }\n                };\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `neon-border ${getConfidenceBorder(anomaly.confidence)} p-2 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"data-stream absolute top-0 left-0 w-full h-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 868,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm\",\n                        children: getIcon(anomaly.type)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 871,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-cyan-300 font-['Exo_2'] text-xs\",\n                          children: anomaly.description\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 873,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xs text-cyan-500 font-['Exo_2']\",\n                          children: anomaly.location\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 874,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 872,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 870,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-right\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `text-xs font-bold ${getConfidenceColor(anomaly.confidence)} font-['Orbitron']`,\n                        children: [anomaly.confidence, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 878,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 877,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 869,\n                    columnNumber: 23\n                  }, this)]\n                }, anomaly.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 864,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 831,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"neon-border rounded-lg p-4 relative overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"data-stream absolute top-0 left-0 w-full h-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 891,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase\",\n              children: \"System Recommendations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 892,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3 h-48 overflow-y-auto\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"neon-border p-3 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"data-stream absolute top-0 left-0 w-full h-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 897,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-cyan-300 font-['Exo_2'] text-xs\",\n                    children: \"Optimize CRAC parameters Sector A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 899,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-green-400 font-['Orbitron'] font-bold text-xs\",\n                    children: \"\\u25CF\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 900,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 898,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 896,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"neon-border p-3 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"data-stream absolute top-0 left-0 w-full h-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 904,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-cyan-300 font-['Exo_2'] text-xs\",\n                    children: \"Investigate thermal sensors Rack M\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 906,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-yellow-400 font-['Orbitron'] font-bold text-xs\",\n                    children: \"\\u25CF\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 907,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 905,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 903,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"neon-border p-3 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"data-stream absolute top-0 left-0 w-full h-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 911,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-cyan-300 font-['Exo_2'] text-xs\",\n                    children: \"Check power distribution Unit B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 913,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-red-400 font-['Orbitron'] font-bold text-xs\",\n                    children: \"\\u25CF\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 914,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 912,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 910,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 895,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 890,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 736,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 721,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"neon-border rounded-lg p-6 relative overflow-hidden matrix-bg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"scan-line\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 924,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center text-sm relative z-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-cyan-400 font-['Exo_2']\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs tracking-wider uppercase\",\n              children: \"Last Neural Sync:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hologram-text font-['Orbitron'] text-lg\",\n              children: currentTime.toLocaleTimeString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 928,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 926,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-4 h-4 bg-green-400 rounded-full animate-pulse pulse-glow\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 932,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-cyan-300 font-['Exo_2'] tracking-wider\",\n                children: \"ALL SYSTEMS OPERATIONAL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 933,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 931,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-cyan-400 font-['Exo_2']\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs tracking-wider uppercase\",\n                children: \"Data Stream:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 936,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hologram-text font-['Orbitron']\",\n                children: \"LIVE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 937,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 935,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(Cpu, {\n                className: \"w-5 h-5 text-cyan-400 animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 940,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-cyan-300 font-['Exo_2']\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs tracking-wider uppercase\",\n                  children: \"Neural Nodes:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 942,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hologram-text font-['Orbitron']\",\n                  children: heatmapData.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 943,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 941,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 939,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 930,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 925,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 923,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 457,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 409,\n    columnNumber: 5\n  }, this);\n};\n_s(UnifiedDataCenterDashboard, \"Qx0dcFmUGcqvtTYsEDuakExIIZM=\");\n_c = UnifiedDataCenterDashboard;\nexport default UnifiedDataCenterDashboard;\nvar _c;\n$RefreshReg$(_c, \"UnifiedDataCenterDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "AreaChart", "Area", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Bell", "Search", "User", "<PERSON><PERSON>", "Cpu", "jsxDEV", "_jsxDEV", "futuristicStyles", "document", "styleElement", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "UnifiedDataCenterDashboard", "_s", "currentTime", "setCurrentTime", "Date", "selected<PERSON><PERSON>", "setSelectedRack", "kpiData", "setKpiData", "pue", "itLoad", "coolingLoad", "trialPower", "upsPower", "pdu<PERSON>ow<PERSON>", "totalAlarms", "activeAlarms", "alarms", "setAlarms", "heatmapData", "setHeatmapData", "anomalies", "setAnomalies", "timeSeriesData", "setTimeSeriesData", "generateKPIData", "now", "hour", "getHours", "minute", "getMinutes", "timeVariation", "Math", "sin", "PI", "random", "floor", "generateAlarmData", "alarmTypes", "locations", "messages", "Critical", "Warning", "Info", "numAlarms", "i", "type", "length", "location", "messageList", "message", "time", "toLocaleTimeString", "push", "id", "status", "sort", "a", "b", "priority", "generateHeatmapData", "data", "baseTemp", "row", "col", "distanceFromCenter", "sqrt", "pow", "hotSpotInfluence", "randomVariation", "temperature", "power", "x", "y", "max", "min", "generateAnomalies", "anomalyTypes", "icon", "description", "numAnomalies", "anomaly", "confidence", "severity", "generateTimeSeriesData", "getTime", "cooling", "effectiveness", "timer", "setInterval", "clearInterval", "interval", "getTemperatureColor", "temp", "getAlarmColor", "KPIGauge", "title", "value", "unit", "color", "percentage", "strokeColor", "glowColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "viewBox", "stdDeviation", "result", "in", "d", "fill", "stroke", "strokeWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeLinecap", "style", "filter", "round", "placeholder", "onClick", "toggleFullScreen", "strokeLinejoin", "toFixed", "width", "height", "slice", "x1", "y1", "x2", "y2", "offset", "stopColor", "stopOpacity", "dataKey", "axisLine", "tickLine", "tick", "fontSize", "fontFamily", "domain", "contentStyle", "backgroundColor", "border", "borderRadius", "fillOpacity", "map", "rack", "boxShadow", "alarm", "getAlarmIcon", "getAlarmBorderColor", "getAlarmTextColor", "toUpperCase", "dot", "getConfidenceColor", "getConfidenceBorder", "getIcon", "_c", "$RefreshReg$"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/src/components/UnifiedDataCenterDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON>Chart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';\nimport { AlertTriangle, Thermometer, Zap, Server, Shield, TrendingUp, Settings, Bell, Search, User, Menu, Activity, Cpu, HardDrive, Wifi } from 'lucide-react';\n\n// Futuristic CSS styles\nconst futuristicStyles = `\n  @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap');\n  \n  .futuristic-bg {\n    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0a0a0a 100%);\n    min-height: 100vh;\n    position: relative;\n    overflow-x: hidden;\n  }\n  \n  .futuristic-bg::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: \n      radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),\n      radial-gradient(circle at 40% 60%, rgba(0, 255, 0, 0.05) 0%, transparent 50%);\n    pointer-events: none;\n  }\n  \n  .neon-border {\n    border: 1px solid rgba(0, 255, 255, 0.3);\n    box-shadow: \n      0 0 10px rgba(0, 255, 255, 0.2),\n      inset 0 0 10px rgba(0, 255, 255, 0.1);\n    background: rgba(0, 20, 40, 0.8);\n    backdrop-filter: blur(10px);\n  }\n  \n  .neon-glow {\n    box-shadow: \n      0 0 20px rgba(0, 255, 255, 0.4),\n      0 0 40px rgba(0, 255, 255, 0.2),\n      inset 0 0 20px rgba(0, 255, 255, 0.1);\n  }\n  \n  .hologram-text {\n    font-family: 'Orbitron', monospace;\n    color: #00ffff;\n    text-shadow: \n      0 0 10px rgba(0, 255, 255, 0.8),\n      0 0 20px rgba(0, 255, 255, 0.4),\n      0 0 30px rgba(0, 255, 255, 0.2);\n  }\n  \n  .data-stream {\n    background: linear-gradient(90deg, transparent 0%, rgba(0, 255, 255, 0.1) 50%, transparent 100%);\n    animation: dataFlow 3s linear infinite;\n  }\n  \n  @keyframes dataFlow {\n    0% { transform: translateX(-100%); }\n    100% { transform: translateX(100%); }\n  }\n  \n  .pulse-glow {\n    animation: pulseGlow 2s ease-in-out infinite alternate;\n  }\n  \n  @keyframes pulseGlow {\n    from { box-shadow: 0 0 20px rgba(0, 255, 255, 0.4); }\n    to { box-shadow: 0 0 30px rgba(0, 255, 255, 0.8), 0 0 40px rgba(0, 255, 255, 0.4); }\n  }\n  \n  .matrix-bg {\n    background: \n      linear-gradient(90deg, transparent 98%, rgba(0, 255, 255, 0.1) 100%),\n      linear-gradient(0deg, transparent 98%, rgba(0, 255, 255, 0.1) 100%);\n    background-size: 20px 20px;\n  }\n  \n  .scan-line {\n    position: relative;\n    overflow: hidden;\n  }\n  \n  .scan-line::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 2px;\n    background: linear-gradient(90deg, transparent, #00ffff, transparent);\n    animation: scanLine 3s linear infinite;\n  }\n  \n  @keyframes scanLine {\n    0% { left: -100%; }\n    100% { left: 100%; }\n  }\n  \n  .custom-scrollbar::-webkit-scrollbar {\n    width: 8px;\n  }\n  \n  .custom-scrollbar::-webkit-scrollbar-track {\n    background: rgba(0, 20, 40, 0.5);\n    border-radius: 4px;\n  }\n  \n  .custom-scrollbar::-webkit-scrollbar-thumb {\n    background: linear-gradient(180deg, #00ffff, #0080ff);\n    border-radius: 4px;\n    box-shadow: 0 0 10px rgba(0, 255, 255, 0.4);\n  }\n  \n  .custom-scrollbar::-webkit-scrollbar-thumb:hover {\n    background: linear-gradient(180deg, #00ffff, #00aaff);\n    box-shadow: 0 0 15px rgba(0, 255, 255, 0.6);\n  }\n  \n  /* Responsive adjustments */\n  @media (max-width: 768px) {\n    .hologram-text {\n      font-size: 0.9em;\n    }\n    \n    .neon-border {\n      padding: 1rem;\n    }\n  }\n`;\n\n// Inject styles\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style');\n  styleElement.textContent = futuristicStyles;\n  document.head.appendChild(styleElement);\n}\n\nconst UnifiedDataCenterDashboard = () => {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [selectedRack, setSelectedRack] = useState(null);\n  const [kpiData, setKpiData] = useState({\n    pue: 1.45,\n    itLoad: 1.1,\n    coolingLoad: 1.1,\n    trialPower: 2.4,\n    upsPower: 1.11,\n    pduPower: 1.5,\n    totalAlarms: 4,\n    activeAlarms: 2\n  });\n  const [alarms, setAlarms] = useState([]);\n  const [heatmapData, setHeatmapData] = useState([]);\n  const [anomalies, setAnomalies] = useState([]);\n  const [timeSeriesData, setTimeSeriesData] = useState([]);\n\n  // Generate realistic KPI data\n  const generateKPIData = () => {\n    const now = new Date();\n    const hour = now.getHours();\n    const minute = now.getMinutes();\n    const timeVariation = Math.sin((hour * 60 + minute) * Math.PI / 720);\n\n    return {\n      pue: 1.42 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.02,\n      itLoad: 1.08 + timeVariation * 0.15 + (Math.random() - 0.5) * 0.03,\n      coolingLoad: 1.1 + timeVariation * 0.08 + (Math.random() - 0.5) * 0.02,\n      trialPower: 2.35 + timeVariation * 0.2 + (Math.random() - 0.5) * 0.05,\n      upsPower: 1.1 + (Math.random() - 0.5) * 0.01,\n      pduPower: 1.48 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.02,\n      totalAlarms: Math.floor(3 + Math.random() * 5),\n      activeAlarms: Math.floor(1 + Math.random() * 3)\n    };\n  };\n\n  // Generate alarm data\n  const generateAlarmData = () => {\n    const alarmTypes = ['Critical', 'Warning', 'Info'];\n    const locations = ['Rack A1', 'Rack B3', 'UPS Room', 'Cooling Unit 2', 'Switch Core', 'PDU Zone C'];\n    const messages = {\n      Critical: ['Temperature Threshold Exceeded', 'Power Supply Failure', 'Cooling System Down'],\n      Warning: ['High Humidity Detected', 'UPS Battery Low', 'Network Latency High'],\n      Info: ['Scheduled Maintenance', 'System Update Complete', 'Backup Process Started']\n    };\n\n    const numAlarms = 3 + Math.floor(Math.random() * 4);\n    const alarms = [];\n\n    for (let i = 0; i < numAlarms; i++) {\n      const type = alarmTypes[Math.floor(Math.random() * alarmTypes.length)];\n      const location = locations[Math.floor(Math.random() * locations.length)];\n      const messageList = messages[type];\n      const message = messageList[Math.floor(Math.random() * messageList.length)];\n      const time = new Date(Date.now() - Math.random() * 3600000).toLocaleTimeString();\n\n      alarms.push({\n        id: i + 1,\n        type,\n        message,\n        location,\n        time,\n        status: Math.random() > 0.5 ? 'Active' : 'Acknowledged'\n      });\n    }\n\n    return alarms.sort((a, b) => {\n      const priority = { Critical: 3, Warning: 2, Info: 1 };\n      return priority[b.type] - priority[a.type];\n    });\n  };\n\n  // Generate heatmap data\n  const generateHeatmapData = () => {\n    const data = [];\n    const baseTemp = 22;\n    const now = Date.now();\n\n    for (let row = 0; row < 8; row++) {\n      for (let col = 0; col < 10; col++) {\n        const distanceFromCenter = Math.sqrt(Math.pow(col - 4.5, 2) + Math.pow(row - 3.5, 2));\n        const hotSpotInfluence = Math.sin(now / 10000 + row * col) * 2;\n        const randomVariation = (Math.random() - 0.5) * 3;\n        const temperature = baseTemp + distanceFromCenter * 0.8 + hotSpotInfluence + randomVariation;\n        const power = 60 + Math.random() * 30 + Math.sin(now / 8000 + row + col) * 10;\n\n        data.push({\n          x: col,\n          y: row,\n          temperature: Math.max(18, Math.min(32, temperature)),\n          power: Math.max(20, Math.min(100, power)),\n          id: `R${row + 1}C${col + 1}`,\n          status: temperature > 28 ? 'critical' : temperature > 25 ? 'warning' : 'normal'\n        });\n      }\n    }\n\n    return data;\n  };\n\n  // Generate anomaly data\n  const generateAnomalies = () => {\n    const anomalyTypes = [\n      { icon: 'power', description: 'Unusual power consumption pattern' },\n      { icon: 'temperature', description: 'Temperature gradient anomaly' },\n      { icon: 'network', description: 'Network bandwidth surge detected' },\n      { icon: 'cooling', description: 'HVAC performance degradation' }\n    ];\n\n    const locations = ['Rack A3-A5', 'Zone B, Rows 3-4', 'Core switches', 'Cooling Unit 1'];\n    const numAnomalies = 2 + Math.floor(Math.random() * 3);\n    const anomalies = [];\n\n    for (let i = 0; i < numAnomalies; i++) {\n      const anomaly = anomalyTypes[Math.floor(Math.random() * anomalyTypes.length)];\n      const location = locations[Math.floor(Math.random() * locations.length)];\n      const confidence = 75 + Math.floor(Math.random() * 25);\n\n      anomalies.push({\n        id: i + 1,\n        ...anomaly,\n        location,\n        confidence,\n        severity: confidence > 90 ? 'critical' : confidence > 80 ? 'warning' : 'info'\n      });\n    }\n\n    return anomalies.sort((a, b) => b.confidence - a.confidence);\n  };\n\n  // Generate time series data\n  const generateTimeSeriesData = () => {\n    const data = [];\n    const now = new Date();\n\n    for (let i = 23; i >= 0; i--) {\n      const time = new Date(now.getTime() - i * 60 * 60 * 1000);\n      const hour = time.getHours();\n      const timeVariation = Math.sin((hour * Math.PI) / 12);\n\n      data.push({\n        time: time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),\n        power: 1.4 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.05,\n        cooling: 1.1 + timeVariation * 0.08 + (Math.random() - 0.5) * 0.03,\n        temperature: 23 + timeVariation * 1.5 + (Math.random() - 0.5) * 0.8,\n        effectiveness: 85 + timeVariation * 5 + (Math.random() - 0.5) * 3\n      });\n    }\n\n    return data;\n  };\n\n  // Initialize data on component mount\n  useEffect(() => {\n    setKpiData(generateKPIData());\n    setAlarms(generateAlarmData());\n    setHeatmapData(generateHeatmapData());\n    setAnomalies(generateAnomalies());\n    setTimeSeriesData(generateTimeSeriesData());\n  }, []);\n\n  // Update time and KPI data every second\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n      setKpiData(generateKPIData());\n      setTimeSeriesData(generateTimeSeriesData());\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, []);\n\n  // Update heatmap every 3 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setHeatmapData(generateHeatmapData());\n    }, 3000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Update alarms every 15 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setAlarms(generateAlarmData());\n    }, 15000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Update anomalies every 20 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setAnomalies(generateAnomalies());\n    }, 20000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const getTemperatureColor = (temp) => {\n    if (temp < 20) return '#4ade80';\n    if (temp < 22) return '#84cc16';\n    if (temp < 24) return '#eab308';\n    if (temp < 26) return '#f97316';\n    if (temp < 28) return '#ef4444';\n    return '#dc2626';\n  };\n\n  const getAlarmColor = (type) => {\n    switch (type) {\n      case 'Critical': return 'bg-red-50 text-red-700 border-red-200';\n      case 'Warning': return 'bg-yellow-50 text-yellow-700 border-yellow-200';\n      case 'Info': return 'bg-blue-50 text-blue-700 border-blue-200';\n      default: return 'text-gray-600 bg-gray-50';\n    }\n  };\n\n  const KPIGauge = ({ title, value, unit, max, color = 'blue' }) => {\n    const percentage = (value / max) * 100;\n    const strokeColor = color === 'green' ? '#00ff88' : color === 'red' ? '#ff0044' : '#00ffff';\n    const glowColor = color === 'green' ? 'rgba(0, 255, 136, 0.4)' : color === 'red' ? 'rgba(255, 0, 68, 0.4)' : 'rgba(0, 255, 255, 0.4)';\n\n    return (\n      <div className=\"neon-border p-6 rounded-lg relative overflow-hidden matrix-bg\">\n        <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n        <div className=\"text-sm text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase\">{title}</div>\n        <div className=\"flex items-center justify-between\">\n          <div className=\"text-3xl font-bold hologram-text font-['Orbitron']\">\n            {value}<span className=\"text-lg text-cyan-300\">{unit}</span>\n          </div>\n          <div className=\"w-20 h-20 relative\">\n            <svg className=\"w-20 h-20 transform -rotate-90\" viewBox=\"0 0 36 36\">\n              <defs>\n                <filter id={`glow-${title}`}>\n                  <feGaussianBlur stdDeviation=\"3\" result=\"coloredBlur\"/>\n                  <feMerge>\n                    <feMergeNode in=\"coloredBlur\"/>\n                    <feMergeNode in=\"SourceGraphic\"/>\n                  </feMerge>\n                </filter>\n              </defs>\n              <path\n                d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                fill=\"none\"\n                stroke=\"rgba(0, 255, 255, 0.2)\"\n                strokeWidth=\"2\"\n              />\n              <path\n                d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                fill=\"none\"\n                stroke={strokeColor}\n                strokeWidth=\"3\"\n                strokeDasharray={`${percentage}, 100`}\n                strokeLinecap=\"round\"\n                style={{ filter: `drop-shadow(0 0 10px ${glowColor})` }}\n              />\n            </svg>\n            <div className=\"absolute inset-0 flex items-center justify-center\">\n              <span className=\"text-sm font-bold hologram-text font-['Orbitron']\">{Math.round(percentage)}%</span>\n            </div>\n          </div>\n        </div>\n        <div className=\"mt-2 text-xs text-cyan-500 font-['Exo_2']\">\n          MAX: {max}{unit}\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"futuristic-bg min-h-screen\">\n      {/* Header */}\n      <header className=\"neon-border backdrop-blur-md px-6 py-4 relative\">\n        <div className=\"scan-line\"></div>\n        <div className=\"flex items-center justify-between relative z-10\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"relative\">\n              <Menu className=\"w-8 h-8 text-cyan-400 pulse-glow cursor-pointer hover:text-cyan-300 transition-colors\" />\n              <div className=\"absolute inset-0 w-8 h-8 border border-cyan-400 rounded opacity-30 animate-pulse\"></div>\n            </div>\n            <div>\n              <h1 className=\"text-2xl font-bold hologram-text font-['Orbitron']\">\n                UNIFIED DATA CENTER\n              </h1>\n              <div className=\"text-sm text-cyan-300 font-['Exo_2'] tracking-wider\">\n                NEURAL COMMAND INTERFACE\n              </div>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-6\">\n            <div className=\"relative neon-border rounded-lg px-4 py-2\">\n              <Search className=\"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-cyan-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search neural networks...\"\n                className=\"pl-10 pr-4 py-1 bg-transparent text-cyan-300 placeholder-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-400 font-['Exo_2']\"\n              />\n            </div>\n            <div className=\"text-sm text-cyan-300 font-['Exo_2'] font-mono\">\n              <div className=\"text-xs text-cyan-500\">SYSTEM TIME</div>\n              <div className=\"hologram-text\">\n                {currentTime.toLocaleTimeString()}\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"relative\">\n                <Bell className=\"w-6 h-6 text-cyan-400 hover:text-cyan-300 transition-colors cursor-pointer\" />\n                <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse\"></div>\n              </div>\n              <div className=\"relative\">\n                <User className=\"w-6 h-6 text-cyan-400 hover:text-cyan-300 transition-colors cursor-pointer\" />\n                <div className=\"absolute inset-0 w-6 h-6 border border-cyan-400 rounded-full animate-ping opacity-20\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"p-6 space-y-6 relative\">\n        {/* Executive Overview - Full Width */}\n        <div className=\"neon-border rounded-lg p-6 relative overflow-hidden matrix-bg\">\n          <button\n            className=\"absolute top-4 right-4 z-10 neon-border p-2 rounded-lg text-cyan-400 hover:text-cyan-300 hover:neon-glow transition-all duration-300\"\n            onClick={() => toggleFullScreen('executive-overview')}\n            title=\"Toggle Full Screen\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\" />\n            </svg>\n          </button>\n            <div className=\"scan-line\"></div>\n            <h2 className=\"text-xl font-bold mb-6 hologram-text font-['Orbitron'] tracking-wider\">\n              EXECUTIVE OVERVIEW\n            </h2>\n\n            {/* KPI Cards */}\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n              <KPIGauge title=\"PUE\" value={kpiData.pue.toFixed(2)} unit=\"\" max={3} color=\"green\" />\n              <KPIGauge title=\"IT Load\" value={kpiData.itLoad.toFixed(1)} unit=\"M\" max={2} color=\"blue\" />\n              <KPIGauge title=\"Cooling Load\" value={Math.round(kpiData.coolingLoad * 650)} unit=\" RT\" max={1000} color=\"blue\" />\n              <div className=\"neon-border p-4 rounded-lg relative overflow-hidden matrix-bg\">\n                <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n                <div className=\"text-sm text-cyan-400 mb-2 font-['Exo_2'] tracking-wider uppercase\">Alarms</div>\n                <div className=\"text-2xl font-bold text-red-400 hologram-text font-['Orbitron']\">{kpiData.totalAlarms}</div>\n                <div className=\"text-xs text-cyan-500 font-['Exo_2']\">{kpiData.activeAlarms} Active</div>\n                <div className=\"absolute top-2 right-2\">\n                  <AlertTriangle className=\"w-5 h-5 text-red-400 animate-pulse\" />\n                </div>\n              </div>\n            </div>\n\n            {/* Thermal Effectiveness Chart */}\n            <div className=\"mb-6\">\n              <h3 className=\"text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase\">\n                Thermal Effectiveness Matrix\n              </h3>\n              <div className=\"h-40 neon-border rounded-lg p-4 relative overflow-hidden\">\n                <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <AreaChart data={timeSeriesData.slice(-12)}>\n                    <defs>\n                      <linearGradient id=\"colorEffectiveness\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"1\">\n                        <stop offset=\"5%\" stopColor=\"#00ffff\" stopOpacity={0.8}/>\n                        <stop offset=\"95%\" stopColor=\"#00ffff\" stopOpacity={0.1}/>\n                      </linearGradient>\n                    </defs>\n                    <CartesianGrid strokeDasharray=\"2 2\" stroke=\"rgba(0, 255, 255, 0.2)\" />\n                    <XAxis\n                      dataKey=\"time\"\n                      axisLine={false}\n                      tickLine={false}\n                      tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}\n                    />\n                    <YAxis\n                      domain={[75, 95]}\n                      axisLine={false}\n                      tickLine={false}\n                      tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}\n                    />\n                    <Tooltip\n                      contentStyle={{\n                        backgroundColor: 'rgba(0, 20, 40, 0.9)',\n                        border: '1px solid rgba(0, 255, 255, 0.3)',\n                        borderRadius: '8px',\n                        color: '#00ffff',\n                        fontFamily: 'Exo 2'\n                      }}\n                    />\n                    <Area\n                      type=\"monotone\"\n                      dataKey=\"effectiveness\"\n                      stroke=\"#00ffff\"\n                      strokeWidth={2}\n                      fillOpacity={1}\n                      fill=\"url(#colorEffectiveness)\"\n                      style={{ filter: 'drop-shadow(0 0 10px rgba(0, 255, 255, 0.4))' }}\n                    />\n                  </AreaChart>\n                </ResponsiveContainer>\n              </div>\n            </div>\n        </div>\n\n        {/* Asset 360 - Thermal Heatmap - Full Width */}\n        <div className=\"neon-border rounded-lg p-6 relative overflow-hidden matrix-bg\">\n          <button\n            className=\"absolute top-4 right-4 z-10 neon-border p-2 rounded-lg text-cyan-400 hover:text-cyan-300 hover:neon-glow transition-all duration-300\"\n            onClick={() => toggleFullScreen('thermal-heatmap')}\n            title=\"Toggle Full Screen\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\" />\n            </svg>\n          </button>\n          <div className=\"scan-line\"></div>\n          <h2 className=\"text-xl font-bold mb-2 hologram-text font-['Orbitron'] tracking-wider\">\n            ASSET 360 - THERMAL HEATMAP\n          </h2>\n          <p className=\"text-sm text-cyan-400 mb-6 font-['Exo_2'] tracking-wider\">\n            Data Hall Temperature Distribution\n          </p>\n\n          {/* Color Legend */}\n          <div className=\"mb-4 flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-xs text-cyan-400 font-['Exo_2'] tracking-wider uppercase\">Temperature Scale:</span>\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-xs text-cyan-300 font-['Exo_2']\">COOL</span>\n                <div className=\"flex space-x-1\">\n                  <div className=\"w-4 h-4 rounded\" style={{ backgroundColor: '#4ade80' }}></div>\n                  <div className=\"w-4 h-4 rounded\" style={{ backgroundColor: '#84cc16' }}></div>\n                  <div className=\"w-4 h-4 rounded\" style={{ backgroundColor: '#eab308' }}></div>\n                  <div className=\"w-4 h-4 rounded\" style={{ backgroundColor: '#f97316' }}></div>\n                  <div className=\"w-4 h-4 rounded\" style={{ backgroundColor: '#ef4444' }}></div>\n                  <div className=\"w-4 h-4 rounded\" style={{ backgroundColor: '#dc2626' }}></div>\n                </div>\n                <span className=\"text-xs text-cyan-300 font-['Exo_2']\">HOT</span>\n              </div>\n            </div>\n            <div className=\"text-xs text-cyan-500 font-['Exo_2']\">\n              Click rack for details\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-10 gap-1 mb-4\">\n            {heatmapData.map((rack) => (\n              <div\n                key={rack.id}\n                className=\"aspect-square rounded cursor-pointer transition-all duration-300 hover:scale-110 relative group\"\n                style={{\n                  backgroundColor: getTemperatureColor(rack.temperature),\n                  boxShadow: `0 0 10px ${getTemperatureColor(rack.temperature)}40`\n                }}\n                onClick={() => setSelectedRack(rack)}\n              >\n                <div className=\"absolute inset-0 bg-black bg-opacity-20 rounded\"></div>\n                <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-cyan-400 bg-opacity-20 rounded\"></div>\n                {/* Rack Number and Temperature Display */}\n                <div className=\"absolute inset-0 flex flex-col items-center justify-center p-1\">\n                  <span className=\"text-xs font-bold text-white drop-shadow-lg font-['Orbitron'] leading-tight\">\n                    {rack.id}\n                  </span>\n                  <span className=\"text-xs font-semibold text-white drop-shadow-lg font-['Exo_2'] leading-tight\">\n                    {rack.temperature.toFixed(1)}°C\n                  </span>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {selectedRack && (\n            <div className=\"neon-border p-4 rounded-lg relative overflow-hidden\">\n              <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n              <h4 className=\"font-bold text-cyan-400 mb-2 font-['Orbitron']\">\n                Neural Node: {selectedRack.id}\n              </h4>\n              <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                <div>\n                  <span className=\"text-cyan-500 font-['Exo_2']\">Temperature:</span>\n                  <div className=\"hologram-text font-['Orbitron']\">\n                    {selectedRack.temperature.toFixed(1)}°C\n                  </div>\n                </div>\n                <div>\n                  <span className=\"text-cyan-500 font-['Exo_2']\">Power:</span>\n                  <div className=\"hologram-text font-['Orbitron']\">\n                    {selectedRack.power.toFixed(0)}%\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Recent Alarms Section */}\n        <div className=\"neon-border rounded-lg p-6 relative overflow-hidden matrix-bg\">\n          <div className=\"scan-line\"></div>\n          <h2 className=\"text-xl font-bold mb-6 hologram-text font-['Orbitron'] tracking-wider\">\n            RECENT ALARMS\n          </h2>\n\n          <div className=\"space-y-3\">\n            {alarms.slice(0, 5).map((alarm) => {\n              const getAlarmIcon = () => {\n                switch (alarm.type) {\n                  case 'Critical':\n                    return <AlertTriangle className=\"w-5 h-5 text-red-400 animate-pulse\" />;\n                  case 'Warning':\n                    return <AlertTriangle className=\"w-5 h-5 text-yellow-400\" />;\n                  default:\n                    return <AlertTriangle className=\"w-5 h-5 text-blue-400\" />;\n                }\n              };\n\n              const getAlarmBorderColor = () => {\n                switch (alarm.type) {\n                  case 'Critical':\n                    return 'border-red-400';\n                  case 'Warning':\n                    return 'border-yellow-400';\n                  default:\n                    return 'border-blue-400';\n                }\n              };\n\n              const getAlarmTextColor = () => {\n                switch (alarm.type) {\n                  case 'Critical':\n                    return 'text-red-400';\n                  case 'Warning':\n                    return 'text-yellow-400';\n                  default:\n                    return 'text-blue-400';\n                }\n              };\n\n              return (\n                <div\n                  key={alarm.id}\n                  className={`neon-border ${getAlarmBorderColor()} p-4 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300`}\n                >\n                  <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-4\">\n                      {getAlarmIcon()}\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-3 mb-1\">\n                          <span className={`text-sm font-bold ${getAlarmTextColor()} font-['Orbitron'] tracking-wider`}>\n                            {alarm.type.toUpperCase()}\n                          </span>\n                          <span className=\"text-xs text-cyan-500 font-['Exo_2']\">\n                            {alarm.location}\n                          </span>\n                        </div>\n                        <div className=\"text-sm text-cyan-300 font-['Exo_2']\">\n                          {alarm.message}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-xs text-cyan-400 font-['Exo_2'] mb-1\">\n                        {alarm.time}\n                      </div>\n                      <div className={`text-xs font-bold ${alarm.status === 'Active' ? 'text-red-400' : 'text-green-400'} font-['Orbitron']`}>\n                        {alarm.status.toUpperCase()}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n\n          {/* View All Alarms Button */}\n          <div className=\"mt-6 text-center\">\n            <button className=\"neon-border px-6 py-2 rounded-lg text-cyan-400 hover:text-cyan-300 hover:neon-glow transition-all duration-300 font-['Exo_2'] tracking-wider uppercase text-sm\">\n              View All Alarms ({alarms.length})\n            </button>\n          </div>\n        </div>\n\n        {/* Predictive AI Insights */}\n        <div className=\"neon-border rounded-lg p-6 relative overflow-hidden matrix-bg\">\n          <button\n            className=\"absolute top-4 right-4 z-10 neon-border p-2 rounded-lg text-cyan-400 hover:text-cyan-300 hover:neon-glow transition-all duration-300\"\n            onClick={() => toggleFullScreen('predictive-ai')}\n            title=\"Toggle Full Screen\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\" />\n            </svg>\n          </button>\n          <div className=\"scan-line\"></div>\n          <h2 className=\"text-xl font-bold mb-6 hologram-text font-['Orbitron'] tracking-wider\">\n            PREDICTIVE AI INSIGHTS\n          </h2>\n\n          <div className=\"space-y-6\">\n            {/* 1. Cooling Forecast - Full Width Row */}\n            <div className=\"neon-border rounded-lg p-6 relative overflow-hidden\">\n              <button\n                className=\"absolute top-4 right-4 z-10 neon-border p-2 rounded-lg text-cyan-400 hover:text-cyan-300 hover:neon-glow transition-all duration-300\"\n                onClick={() => toggleFullScreen('cooling-forecast')}\n                title=\"Toggle Full Screen\"\n              >\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\" />\n                </svg>\n              </button>\n              <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n              <h3 className=\"text-lg font-medium text-cyan-400 mb-4 font-['Exo_2'] tracking-wider uppercase\">\n                Cooling Forecast\n              </h3>\n              <div className=\"h-64\">\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <LineChart data={timeSeriesData.slice(-12)}>\n                    <CartesianGrid strokeDasharray=\"2 2\" stroke=\"rgba(0, 255, 255, 0.2)\" />\n                    <XAxis\n                      dataKey=\"time\"\n                      axisLine={false}\n                      tickLine={false}\n                      tick={{ fill: '#00ffff', fontSize: 11, fontFamily: 'Exo 2' }}\n                    />\n                    <YAxis\n                      domain={[20, 26]}\n                      axisLine={false}\n                      tickLine={false}\n                      tick={{ fill: '#00ffff', fontSize: 11, fontFamily: 'Exo 2' }}\n                    />\n                    <Tooltip\n                      contentStyle={{\n                        backgroundColor: 'rgba(0, 20, 40, 0.9)',\n                        border: '1px solid rgba(0, 255, 255, 0.3)',\n                        borderRadius: '8px',\n                        color: '#00ffff',\n                        fontFamily: 'Exo 2'\n                      }}\n                    />\n                    <Line\n                      type=\"monotone\"\n                      dataKey=\"temperature\"\n                      stroke=\"#f59e0b\"\n                      strokeWidth={3}\n                      dot={false}\n                      style={{ filter: 'drop-shadow(0 0 10px rgba(245, 158, 11, 0.6))' }}\n                    />\n                  </LineChart>\n                </ResponsiveContainer>\n              </div>\n            </div>\n\n            {/* 2. Recommendations - Full Width Row */}\n            <div className=\"neon-border rounded-lg p-6 relative overflow-hidden\">\n              <button\n                className=\"absolute top-4 right-4 z-10 neon-border p-2 rounded-lg text-cyan-400 hover:text-cyan-300 hover:neon-glow transition-all duration-300\"\n                onClick={() => toggleFullScreen('recommendations')}\n                title=\"Toggle Full Screen\"\n              >\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\" />\n                </svg>\n              </button>\n              <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n              <h3 className=\"text-lg font-medium text-cyan-400 mb-4 font-['Exo_2'] tracking-wider uppercase\">\n                Recommendations\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div className=\"neon-border p-4 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300\">\n                  <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-cyan-300 font-['Exo_2'] text-sm\">Optimize I/O airflows Sector A</span>\n                    <span className=\"text-green-400 font-['Orbitron'] font-bold text-sm\">EXEC A</span>\n                  </div>\n                </div>\n                <div className=\"neon-border p-4 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300\">\n                  <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-cyan-300 font-['Exo_2'] text-sm\">Monitor return chilled water</span>\n                    <span className=\"text-yellow-400 font-['Orbitron'] font-bold text-sm\">CHECK B</span>\n                  </div>\n                </div>\n                <div className=\"neon-border p-4 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300\">\n                  <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-cyan-300 font-['Exo_2'] text-sm\">Server space cooling pump</span>\n                    <span className=\"text-blue-400 font-['Orbitron'] font-bold text-sm\">CHECK A</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* 3. Anomaly Detection */}\n            <div className=\"neon-border rounded-lg p-4 relative overflow-hidden\">\n              <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n              <h3 className=\"text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase\">\n                Anomaly Detection\n              </h3>\n              <div className=\"space-y-2 h-48 overflow-y-auto\">\n                {anomalies.slice(0, 4).map((anomaly) => {\n                  const getConfidenceColor = (confidence: number) => {\n                    if (confidence >= 90) return 'text-red-400';\n                    if (confidence >= 70) return 'text-yellow-400';\n                    return 'text-green-400';\n                  };\n\n                  const getConfidenceBorder = (confidence: number) => {\n                    if (confidence >= 90) return 'border-red-400';\n                    if (confidence >= 70) return 'border-yellow-400';\n                    return 'border-green-400';\n                  };\n\n                  const getIcon = (type: string) => {\n                    switch (type) {\n                      case 'power':\n                        return '⚡';\n                      case 'network':\n                        return '🌐';\n                      case 'temperature':\n                        return '🌡️';\n                      default:\n                        return '⚠️';\n                    }\n                  };\n\n                  return (\n                    <div\n                      key={anomaly.id}\n                      className={`neon-border ${getConfidenceBorder(anomaly.confidence)} p-2 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300`}\n                    >\n                      <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n                      <div className=\"flex justify-between items-center\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-sm\">{getIcon(anomaly.type)}</span>\n                          <div>\n                            <div className=\"text-cyan-300 font-['Exo_2'] text-xs\">{anomaly.description}</div>\n                            <div className=\"text-xs text-cyan-500 font-['Exo_2']\">{anomaly.location}</div>\n                          </div>\n                        </div>\n                        <div className=\"text-right\">\n                          <div className={`text-xs font-bold ${getConfidenceColor(anomaly.confidence)} font-['Orbitron']`}>\n                            {anomaly.confidence}%\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n\n            {/* 4. System Recommendations */}\n            <div className=\"neon-border rounded-lg p-4 relative overflow-hidden\">\n              <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n              <h3 className=\"text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase\">\n                System Recommendations\n              </h3>\n              <div className=\"space-y-3 h-48 overflow-y-auto\">\n                <div className=\"neon-border p-3 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300\">\n                  <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-cyan-300 font-['Exo_2'] text-xs\">Optimize CRAC parameters Sector A</span>\n                    <span className=\"text-green-400 font-['Orbitron'] font-bold text-xs\">●</span>\n                  </div>\n                </div>\n                <div className=\"neon-border p-3 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300\">\n                  <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-cyan-300 font-['Exo_2'] text-xs\">Investigate thermal sensors Rack M</span>\n                    <span className=\"text-yellow-400 font-['Orbitron'] font-bold text-xs\">●</span>\n                  </div>\n                </div>\n                <div className=\"neon-border p-3 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300\">\n                  <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-cyan-300 font-['Exo_2'] text-xs\">Check power distribution Unit B</span>\n                    <span className=\"text-red-400 font-['Orbitron'] font-bold text-xs\">●</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Status Footer */}\n        <div className=\"neon-border rounded-lg p-6 relative overflow-hidden matrix-bg\">\n          <div className=\"scan-line\"></div>\n          <div className=\"flex justify-between items-center text-sm relative z-10\">\n            <div className=\"text-cyan-400 font-['Exo_2']\">\n              <span className=\"text-xs tracking-wider uppercase\">Last Neural Sync:</span>\n              <div className=\"hologram-text font-['Orbitron'] text-lg\">{currentTime.toLocaleTimeString()}</div>\n            </div>\n            <div className=\"flex items-center space-x-6\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-4 h-4 bg-green-400 rounded-full animate-pulse pulse-glow\"></div>\n                <span className=\"text-cyan-300 font-['Exo_2'] tracking-wider\">ALL SYSTEMS OPERATIONAL</span>\n              </div>\n              <div className=\"text-cyan-400 font-['Exo_2']\">\n                <span className=\"text-xs tracking-wider uppercase\">Data Stream:</span>\n                <div className=\"hologram-text font-['Orbitron']\">LIVE</div>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <Cpu className=\"w-5 h-5 text-cyan-400 animate-pulse\" />\n                <span className=\"text-cyan-300 font-['Exo_2']\">\n                  <span className=\"text-xs tracking-wider uppercase\">Neural Nodes:</span>\n                  <div className=\"hologram-text font-['Orbitron']\">{heatmapData.length}</div>\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default UnifiedDataCenterDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,SAAS,EAAEC,IAAI,QAAQ,UAAU;AACtH,SAASC,aAAa,EAA0DC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAYC,GAAG,QAAyB,cAAc;;AAE9J;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,gBAAgB,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMC,YAAY,GAAGD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;EACpDD,YAAY,CAACE,WAAW,GAAGJ,gBAAgB;EAC3CC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACJ,YAAY,CAAC;AACzC;AAEA,MAAMK,0BAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,IAAI8B,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC;IACrCmC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,GAAG;IACXC,WAAW,EAAE,GAAG;IAChBC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,GAAG;IACbC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA,MAAMmD,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,GAAG,GAAG,IAAItB,IAAI,CAAC,CAAC;IACtB,MAAMuB,IAAI,GAAGD,GAAG,CAACE,QAAQ,CAAC,CAAC;IAC3B,MAAMC,MAAM,GAAGH,GAAG,CAACI,UAAU,CAAC,CAAC;IAC/B,MAAMC,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACN,IAAI,GAAG,EAAE,GAAGE,MAAM,IAAIG,IAAI,CAACE,EAAE,GAAG,GAAG,CAAC;IAEpE,OAAO;MACLzB,GAAG,EAAE,IAAI,GAAGsB,aAAa,GAAG,GAAG,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;MAC9DzB,MAAM,EAAE,IAAI,GAAGqB,aAAa,GAAG,IAAI,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;MAClExB,WAAW,EAAE,GAAG,GAAGoB,aAAa,GAAG,IAAI,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;MACtEvB,UAAU,EAAE,IAAI,GAAGmB,aAAa,GAAG,GAAG,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;MACrEtB,QAAQ,EAAE,GAAG,GAAG,CAACmB,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;MAC5CrB,QAAQ,EAAE,IAAI,GAAGiB,aAAa,GAAG,GAAG,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;MACnEpB,WAAW,EAAEiB,IAAI,CAACI,KAAK,CAAC,CAAC,GAAGJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;MAC9CnB,YAAY,EAAEgB,IAAI,CAACI,KAAK,CAAC,CAAC,GAAGJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,CAAC;IAChD,CAAC;EACH,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,UAAU,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,MAAM,CAAC;IAClD,MAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAE,aAAa,EAAE,YAAY,CAAC;IACnG,MAAMC,QAAQ,GAAG;MACfC,QAAQ,EAAE,CAAC,gCAAgC,EAAE,sBAAsB,EAAE,qBAAqB,CAAC;MAC3FC,OAAO,EAAE,CAAC,wBAAwB,EAAE,iBAAiB,EAAE,sBAAsB,CAAC;MAC9EC,IAAI,EAAE,CAAC,uBAAuB,EAAE,wBAAwB,EAAE,wBAAwB;IACpF,CAAC;IAED,MAAMC,SAAS,GAAG,CAAC,GAAGZ,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACnD,MAAMlB,MAAM,GAAG,EAAE;IAEjB,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,EAAEC,CAAC,EAAE,EAAE;MAClC,MAAMC,IAAI,GAAGR,UAAU,CAACN,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAGG,UAAU,CAACS,MAAM,CAAC,CAAC;MACtE,MAAMC,QAAQ,GAAGT,SAAS,CAACP,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAGI,SAAS,CAACQ,MAAM,CAAC,CAAC;MACxE,MAAME,WAAW,GAAGT,QAAQ,CAACM,IAAI,CAAC;MAClC,MAAMI,OAAO,GAAGD,WAAW,CAACjB,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAGc,WAAW,CAACF,MAAM,CAAC,CAAC;MAC3E,MAAMI,IAAI,GAAG,IAAI/C,IAAI,CAACA,IAAI,CAACsB,GAAG,CAAC,CAAC,GAAGM,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC,CAACiB,kBAAkB,CAAC,CAAC;MAEhFnC,MAAM,CAACoC,IAAI,CAAC;QACVC,EAAE,EAAET,CAAC,GAAG,CAAC;QACTC,IAAI;QACJI,OAAO;QACPF,QAAQ;QACRG,IAAI;QACJI,MAAM,EAAEvB,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAG;MAC3C,CAAC,CAAC;IACJ;IAEA,OAAOlB,MAAM,CAACuC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC3B,MAAMC,QAAQ,GAAG;QAAElB,QAAQ,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAE,CAAC;MACrD,OAAOgB,QAAQ,CAACD,CAAC,CAACZ,IAAI,CAAC,GAAGa,QAAQ,CAACF,CAAC,CAACX,IAAI,CAAC;IAC5C,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMc,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,IAAI,GAAG,EAAE;IACf,MAAMC,QAAQ,GAAG,EAAE;IACnB,MAAMpC,GAAG,GAAGtB,IAAI,CAACsB,GAAG,CAAC,CAAC;IAEtB,KAAK,IAAIqC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;MAChC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,EAAE,EAAEA,GAAG,EAAE,EAAE;QACjC,MAAMC,kBAAkB,GAAGjC,IAAI,CAACkC,IAAI,CAAClC,IAAI,CAACmC,GAAG,CAACH,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAGhC,IAAI,CAACmC,GAAG,CAACJ,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;QACrF,MAAMK,gBAAgB,GAAGpC,IAAI,CAACC,GAAG,CAACP,GAAG,GAAG,KAAK,GAAGqC,GAAG,GAAGC,GAAG,CAAC,GAAG,CAAC;QAC9D,MAAMK,eAAe,GAAG,CAACrC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;QACjD,MAAMmC,WAAW,GAAGR,QAAQ,GAAGG,kBAAkB,GAAG,GAAG,GAAGG,gBAAgB,GAAGC,eAAe;QAC5F,MAAME,KAAK,GAAG,EAAE,GAAGvC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,EAAE,GAAGH,IAAI,CAACC,GAAG,CAACP,GAAG,GAAG,IAAI,GAAGqC,GAAG,GAAGC,GAAG,CAAC,GAAG,EAAE;QAE7EH,IAAI,CAACR,IAAI,CAAC;UACRmB,CAAC,EAAER,GAAG;UACNS,CAAC,EAAEV,GAAG;UACNO,WAAW,EAAEtC,IAAI,CAAC0C,GAAG,CAAC,EAAE,EAAE1C,IAAI,CAAC2C,GAAG,CAAC,EAAE,EAAEL,WAAW,CAAC,CAAC;UACpDC,KAAK,EAAEvC,IAAI,CAAC0C,GAAG,CAAC,EAAE,EAAE1C,IAAI,CAAC2C,GAAG,CAAC,GAAG,EAAEJ,KAAK,CAAC,CAAC;UACzCjB,EAAE,EAAE,IAAIS,GAAG,GAAG,CAAC,IAAIC,GAAG,GAAG,CAAC,EAAE;UAC5BT,MAAM,EAAEe,WAAW,GAAG,EAAE,GAAG,UAAU,GAAGA,WAAW,GAAG,EAAE,GAAG,SAAS,GAAG;QACzE,CAAC,CAAC;MACJ;IACF;IAEA,OAAOT,IAAI;EACb,CAAC;;EAED;EACA,MAAMe,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,YAAY,GAAG,CACnB;MAAEC,IAAI,EAAE,OAAO;MAAEC,WAAW,EAAE;IAAoC,CAAC,EACnE;MAAED,IAAI,EAAE,aAAa;MAAEC,WAAW,EAAE;IAA+B,CAAC,EACpE;MAAED,IAAI,EAAE,SAAS;MAAEC,WAAW,EAAE;IAAmC,CAAC,EACpE;MAAED,IAAI,EAAE,SAAS;MAAEC,WAAW,EAAE;IAA+B,CAAC,CACjE;IAED,MAAMxC,SAAS,GAAG,CAAC,YAAY,EAAE,kBAAkB,EAAE,eAAe,EAAE,gBAAgB,CAAC;IACvF,MAAMyC,YAAY,GAAG,CAAC,GAAGhD,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACtD,MAAMd,SAAS,GAAG,EAAE;IAEpB,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,YAAY,EAAEnC,CAAC,EAAE,EAAE;MACrC,MAAMoC,OAAO,GAAGJ,YAAY,CAAC7C,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG0C,YAAY,CAAC9B,MAAM,CAAC,CAAC;MAC7E,MAAMC,QAAQ,GAAGT,SAAS,CAACP,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAGI,SAAS,CAACQ,MAAM,CAAC,CAAC;MACxE,MAAMmC,UAAU,GAAG,EAAE,GAAGlD,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;MAEtDd,SAAS,CAACgC,IAAI,CAAC;QACbC,EAAE,EAAET,CAAC,GAAG,CAAC;QACT,GAAGoC,OAAO;QACVjC,QAAQ;QACRkC,UAAU;QACVC,QAAQ,EAAED,UAAU,GAAG,EAAE,GAAG,UAAU,GAAGA,UAAU,GAAG,EAAE,GAAG,SAAS,GAAG;MACzE,CAAC,CAAC;IACJ;IAEA,OAAO7D,SAAS,CAACmC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACwB,UAAU,GAAGzB,CAAC,CAACyB,UAAU,CAAC;EAC9D,CAAC;;EAED;EACA,MAAME,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMvB,IAAI,GAAG,EAAE;IACf,MAAMnC,GAAG,GAAG,IAAItB,IAAI,CAAC,CAAC;IAEtB,KAAK,IAAIyC,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC5B,MAAMM,IAAI,GAAG,IAAI/C,IAAI,CAACsB,GAAG,CAAC2D,OAAO,CAAC,CAAC,GAAGxC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MACzD,MAAMlB,IAAI,GAAGwB,IAAI,CAACvB,QAAQ,CAAC,CAAC;MAC5B,MAAMG,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAEN,IAAI,GAAGK,IAAI,CAACE,EAAE,GAAI,EAAE,CAAC;MAErD2B,IAAI,CAACR,IAAI,CAAC;QACRF,IAAI,EAAEA,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;UAAEzB,IAAI,EAAE,SAAS;UAAEE,MAAM,EAAE;QAAU,CAAC,CAAC;QAC9E0C,KAAK,EAAE,GAAG,GAAGxC,aAAa,GAAG,GAAG,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;QAC/DmD,OAAO,EAAE,GAAG,GAAGvD,aAAa,GAAG,IAAI,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;QAClEmC,WAAW,EAAE,EAAE,GAAGvC,aAAa,GAAG,GAAG,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;QACnEoD,aAAa,EAAE,EAAE,GAAGxD,aAAa,GAAG,CAAC,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI;MAClE,CAAC,CAAC;IACJ;IAEA,OAAO0B,IAAI;EACb,CAAC;;EAED;EACAtF,SAAS,CAAC,MAAM;IACdiC,UAAU,CAACiB,eAAe,CAAC,CAAC,CAAC;IAC7BP,SAAS,CAACmB,iBAAiB,CAAC,CAAC,CAAC;IAC9BjB,cAAc,CAACwC,mBAAmB,CAAC,CAAC,CAAC;IACrCtC,YAAY,CAACsD,iBAAiB,CAAC,CAAC,CAAC;IACjCpD,iBAAiB,CAAC4D,sBAAsB,CAAC,CAAC,CAAC;EAC7C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7G,SAAS,CAAC,MAAM;IACd,MAAMiH,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BtF,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;MAC1BI,UAAU,CAACiB,eAAe,CAAC,CAAC,CAAC;MAC7BD,iBAAiB,CAAC4D,sBAAsB,CAAC,CAAC,CAAC;IAC7C,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMM,aAAa,CAACF,KAAK,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjH,SAAS,CAAC,MAAM;IACd,MAAMoH,QAAQ,GAAGF,WAAW,CAAC,MAAM;MACjCrE,cAAc,CAACwC,mBAAmB,CAAC,CAAC,CAAC;IACvC,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAM8B,aAAa,CAACC,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApH,SAAS,CAAC,MAAM;IACd,MAAMoH,QAAQ,GAAGF,WAAW,CAAC,MAAM;MACjCvE,SAAS,CAACmB,iBAAiB,CAAC,CAAC,CAAC;IAChC,CAAC,EAAE,KAAK,CAAC;IACT,OAAO,MAAMqD,aAAa,CAACC,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApH,SAAS,CAAC,MAAM;IACd,MAAMoH,QAAQ,GAAGF,WAAW,CAAC,MAAM;MACjCnE,YAAY,CAACsD,iBAAiB,CAAC,CAAC,CAAC;IACnC,CAAC,EAAE,KAAK,CAAC;IACT,OAAO,MAAMc,aAAa,CAACC,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,mBAAmB,GAAIC,IAAI,IAAK;IACpC,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,OAAO,SAAS;EAClB,CAAC;EAED,MAAMC,aAAa,GAAIhD,IAAI,IAAK;IAC9B,QAAQA,IAAI;MACV,KAAK,UAAU;QAAE,OAAO,uCAAuC;MAC/D,KAAK,SAAS;QAAE,OAAO,gDAAgD;MACvE,KAAK,MAAM;QAAE,OAAO,0CAA0C;MAC9D;QAAS,OAAO,0BAA0B;IAC5C;EACF,CAAC;EAED,MAAMiD,QAAQ,GAAGA,CAAC;IAAEC,KAAK;IAAEC,KAAK;IAAEC,IAAI;IAAExB,GAAG;IAAEyB,KAAK,GAAG;EAAO,CAAC,KAAK;IAChE,MAAMC,UAAU,GAAIH,KAAK,GAAGvB,GAAG,GAAI,GAAG;IACtC,MAAM2B,WAAW,GAAGF,KAAK,KAAK,OAAO,GAAG,SAAS,GAAGA,KAAK,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;IAC3F,MAAMG,SAAS,GAAGH,KAAK,KAAK,OAAO,GAAG,wBAAwB,GAAGA,KAAK,KAAK,KAAK,GAAG,uBAAuB,GAAG,wBAAwB;IAErI,oBACE3G,OAAA;MAAK+G,SAAS,EAAC,+DAA+D;MAAAC,QAAA,gBAC5EhH,OAAA;QAAK+G,SAAS,EAAC;MAA8C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpEpH,OAAA;QAAK+G,SAAS,EAAC,oEAAoE;QAAAC,QAAA,EAAER;MAAK;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjGpH,OAAA;QAAK+G,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDhH,OAAA;UAAK+G,SAAS,EAAC,oDAAoD;UAAAC,QAAA,GAChEP,KAAK,eAACzG,OAAA;YAAM+G,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAEN;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACNpH,OAAA;UAAK+G,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjChH,OAAA;YAAK+G,SAAS,EAAC,gCAAgC;YAACM,OAAO,EAAC,WAAW;YAAAL,QAAA,gBACjEhH,OAAA;cAAAgH,QAAA,eACEhH,OAAA;gBAAQ8D,EAAE,EAAE,QAAQ0C,KAAK,EAAG;gBAAAQ,QAAA,gBAC1BhH,OAAA;kBAAgBsH,YAAY,EAAC,GAAG;kBAACC,MAAM,EAAC;gBAAa;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACvDpH,OAAA;kBAAAgH,QAAA,gBACEhH,OAAA;oBAAawH,EAAE,EAAC;kBAAa;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC/BpH,OAAA;oBAAawH,EAAE,EAAC;kBAAe;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACPpH,OAAA;cACEyH,CAAC,EAAC,+EAA+E;cACjFC,IAAI,EAAC,MAAM;cACXC,MAAM,EAAC,wBAAwB;cAC/BC,WAAW,EAAC;YAAG;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACFpH,OAAA;cACEyH,CAAC,EAAC,+EAA+E;cACjFC,IAAI,EAAC,MAAM;cACXC,MAAM,EAAEd,WAAY;cACpBe,WAAW,EAAC,GAAG;cACfC,eAAe,EAAE,GAAGjB,UAAU,OAAQ;cACtCkB,aAAa,EAAC,OAAO;cACrBC,KAAK,EAAE;gBAAEC,MAAM,EAAE,wBAAwBlB,SAAS;cAAI;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNpH,OAAA;YAAK+G,SAAS,EAAC,mDAAmD;YAAAC,QAAA,eAChEhH,OAAA;cAAM+G,SAAS,EAAC,mDAAmD;cAAAC,QAAA,GAAExE,IAAI,CAACyF,KAAK,CAACrB,UAAU,CAAC,EAAC,GAAC;YAAA;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNpH,OAAA;QAAK+G,SAAS,EAAC,2CAA2C;QAAAC,QAAA,GAAC,OACpD,EAAC9B,GAAG,EAAEwB,IAAI;MAAA;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,oBACEpH,OAAA;IAAK+G,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBAEzChH,OAAA;MAAQ+G,SAAS,EAAC,iDAAiD;MAAAC,QAAA,gBACjEhH,OAAA;QAAK+G,SAAS,EAAC;MAAW;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjCpH,OAAA;QAAK+G,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAC9DhH,OAAA;UAAK+G,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1ChH,OAAA;YAAK+G,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBhH,OAAA,CAACH,IAAI;cAACkH,SAAS,EAAC;YAAuF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1GpH,OAAA;cAAK+G,SAAS,EAAC;YAAkF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC,eACNpH,OAAA;YAAAgH,QAAA,gBACEhH,OAAA;cAAI+G,SAAS,EAAC,oDAAoD;cAAAC,QAAA,EAAC;YAEnE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpH,OAAA;cAAK+G,SAAS,EAAC,qDAAqD;cAAAC,QAAA,EAAC;YAErE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpH,OAAA;UAAK+G,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1ChH,OAAA;YAAK+G,SAAS,EAAC,2CAA2C;YAAAC,QAAA,gBACxDhH,OAAA,CAACL,MAAM;cAACoH,SAAS,EAAC;YAA0E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/FpH,OAAA;cACEsD,IAAI,EAAC,MAAM;cACX4E,WAAW,EAAC,2BAA2B;cACvCnB,SAAS,EAAC;YAAsI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNpH,OAAA;YAAK+G,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7DhH,OAAA;cAAK+G,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxDpH,OAAA;cAAK+G,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3BtG,WAAW,CAACkD,kBAAkB,CAAC;YAAC;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpH,OAAA;YAAK+G,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ChH,OAAA;cAAK+G,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBhH,OAAA,CAACN,IAAI;gBAACqH,SAAS,EAAC;cAA4E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/FpH,OAAA;gBAAK+G,SAAS,EAAC;cAAwE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,eACNpH,OAAA;cAAK+G,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBhH,OAAA,CAACJ,IAAI;gBAACmH,SAAS,EAAC;cAA4E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/FpH,OAAA;gBAAK+G,SAAS,EAAC;cAAsF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETpH,OAAA;MAAK+G,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErChH,OAAA;QAAK+G,SAAS,EAAC,+DAA+D;QAAAC,QAAA,gBAC5EhH,OAAA;UACE+G,SAAS,EAAC,sIAAsI;UAChJoB,OAAO,EAAEA,CAAA,KAAMC,gBAAgB,CAAC,oBAAoB,CAAE;UACtD5B,KAAK,EAAC,oBAAoB;UAAAQ,QAAA,eAE1BhH,OAAA;YAAK+G,SAAS,EAAC,SAAS;YAACW,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACN,OAAO,EAAC,WAAW;YAAAL,QAAA,eAC5EhH,OAAA;cAAM8H,aAAa,EAAC,OAAO;cAACO,cAAc,EAAC,OAAO;cAACT,WAAW,EAAE,CAAE;cAACH,CAAC,EAAC;YAA2F;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACPpH,OAAA;UAAK+G,SAAS,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjCpH,OAAA;UAAI+G,SAAS,EAAC,uEAAuE;UAAAC,QAAA,EAAC;QAEtF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGLpH,OAAA;UAAK+G,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzDhH,OAAA,CAACuG,QAAQ;YAACC,KAAK,EAAC,KAAK;YAACC,KAAK,EAAE1F,OAAO,CAACE,GAAG,CAACqH,OAAO,CAAC,CAAC,CAAE;YAAC5B,IAAI,EAAC,EAAE;YAACxB,GAAG,EAAE,CAAE;YAACyB,KAAK,EAAC;UAAO;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrFpH,OAAA,CAACuG,QAAQ;YAACC,KAAK,EAAC,SAAS;YAACC,KAAK,EAAE1F,OAAO,CAACG,MAAM,CAACoH,OAAO,CAAC,CAAC,CAAE;YAAC5B,IAAI,EAAC,GAAG;YAACxB,GAAG,EAAE,CAAE;YAACyB,KAAK,EAAC;UAAM;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5FpH,OAAA,CAACuG,QAAQ;YAACC,KAAK,EAAC,cAAc;YAACC,KAAK,EAAEjE,IAAI,CAACyF,KAAK,CAAClH,OAAO,CAACI,WAAW,GAAG,GAAG,CAAE;YAACuF,IAAI,EAAC,KAAK;YAACxB,GAAG,EAAE,IAAK;YAACyB,KAAK,EAAC;UAAM;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClHpH,OAAA;YAAK+G,SAAS,EAAC,+DAA+D;YAAAC,QAAA,gBAC5EhH,OAAA;cAAK+G,SAAS,EAAC;YAA8C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpEpH,OAAA;cAAK+G,SAAS,EAAC,oEAAoE;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChGpH,OAAA;cAAK+G,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAEjG,OAAO,CAACQ;YAAW;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5GpH,OAAA;cAAK+G,SAAS,EAAC,sCAAsC;cAAAC,QAAA,GAAEjG,OAAO,CAACS,YAAY,EAAC,SAAO;YAAA;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzFpH,OAAA;cAAK+G,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrChH,OAAA,CAACP,aAAa;gBAACsH,SAAS,EAAC;cAAoC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpH,OAAA;UAAK+G,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBhH,OAAA;YAAI+G,SAAS,EAAC,gFAAgF;YAAAC,QAAA,EAAC;UAE/F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpH,OAAA;YAAK+G,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACvEhH,OAAA;cAAK+G,SAAS,EAAC;YAA8C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpEpH,OAAA,CAACV,mBAAmB;cAACiJ,KAAK,EAAC,MAAM;cAACC,MAAM,EAAC,MAAM;cAAAxB,QAAA,eAC7ChH,OAAA,CAACT,SAAS;gBAAC8E,IAAI,EAAEtC,cAAc,CAAC0G,KAAK,CAAC,CAAC,EAAE,CAAE;gBAAAzB,QAAA,gBACzChH,OAAA;kBAAAgH,QAAA,eACEhH,OAAA;oBAAgB8D,EAAE,EAAC,oBAAoB;oBAAC4E,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,GAAG;oBAAA7B,QAAA,gBACjEhH,OAAA;sBAAM8I,MAAM,EAAC,IAAI;sBAACC,SAAS,EAAC,SAAS;sBAACC,WAAW,EAAE;oBAAI;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eACzDpH,OAAA;sBAAM8I,MAAM,EAAC,KAAK;sBAACC,SAAS,EAAC,SAAS;sBAACC,WAAW,EAAE;oBAAI;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACPpH,OAAA,CAACZ,aAAa;kBAACyI,eAAe,EAAC,KAAK;kBAACF,MAAM,EAAC;gBAAwB;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvEpH,OAAA,CAACd,KAAK;kBACJ+J,OAAO,EAAC,MAAM;kBACdC,QAAQ,EAAE,KAAM;kBAChBC,QAAQ,EAAE,KAAM;kBAChBC,IAAI,EAAE;oBAAE1B,IAAI,EAAE,SAAS;oBAAE2B,QAAQ,EAAE,EAAE;oBAAEC,UAAU,EAAE;kBAAQ;gBAAE;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC,eACFpH,OAAA,CAACb,KAAK;kBACJoK,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;kBACjBL,QAAQ,EAAE,KAAM;kBAChBC,QAAQ,EAAE,KAAM;kBAChBC,IAAI,EAAE;oBAAE1B,IAAI,EAAE,SAAS;oBAAE2B,QAAQ,EAAE,EAAE;oBAAEC,UAAU,EAAE;kBAAQ;gBAAE;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC,eACFpH,OAAA,CAACX,OAAO;kBACNmK,YAAY,EAAE;oBACZC,eAAe,EAAE,sBAAsB;oBACvCC,MAAM,EAAE,kCAAkC;oBAC1CC,YAAY,EAAE,KAAK;oBACnBhD,KAAK,EAAE,SAAS;oBAChB2C,UAAU,EAAE;kBACd;gBAAE;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFpH,OAAA,CAACR,IAAI;kBACH8D,IAAI,EAAC,UAAU;kBACf2F,OAAO,EAAC,eAAe;kBACvBtB,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAE,CAAE;kBACfgC,WAAW,EAAE,CAAE;kBACflC,IAAI,EAAC,0BAA0B;kBAC/BK,KAAK,EAAE;oBAAEC,MAAM,EAAE;kBAA+C;gBAAE;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNpH,OAAA;QAAK+G,SAAS,EAAC,+DAA+D;QAAAC,QAAA,gBAC5EhH,OAAA;UACE+G,SAAS,EAAC,sIAAsI;UAChJoB,OAAO,EAAEA,CAAA,KAAMC,gBAAgB,CAAC,iBAAiB,CAAE;UACnD5B,KAAK,EAAC,oBAAoB;UAAAQ,QAAA,eAE1BhH,OAAA;YAAK+G,SAAS,EAAC,SAAS;YAACW,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACN,OAAO,EAAC,WAAW;YAAAL,QAAA,eAC5EhH,OAAA;cAAM8H,aAAa,EAAC,OAAO;cAACO,cAAc,EAAC,OAAO;cAACT,WAAW,EAAE,CAAE;cAACH,CAAC,EAAC;YAA2F;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACTpH,OAAA;UAAK+G,SAAS,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjCpH,OAAA;UAAI+G,SAAS,EAAC,uEAAuE;UAAAC,QAAA,EAAC;QAEtF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpH,OAAA;UAAG+G,SAAS,EAAC,0DAA0D;UAAAC,QAAA,EAAC;QAExE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAGJpH,OAAA;UAAK+G,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDhH,OAAA;YAAK+G,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ChH,OAAA;cAAM+G,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzGpH,OAAA;cAAK+G,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1ChH,OAAA;gBAAM+G,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClEpH,OAAA;gBAAK+G,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BhH,OAAA;kBAAK+G,SAAS,EAAC,iBAAiB;kBAACgB,KAAK,EAAE;oBAAE0B,eAAe,EAAE;kBAAU;gBAAE;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9EpH,OAAA;kBAAK+G,SAAS,EAAC,iBAAiB;kBAACgB,KAAK,EAAE;oBAAE0B,eAAe,EAAE;kBAAU;gBAAE;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9EpH,OAAA;kBAAK+G,SAAS,EAAC,iBAAiB;kBAACgB,KAAK,EAAE;oBAAE0B,eAAe,EAAE;kBAAU;gBAAE;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9EpH,OAAA;kBAAK+G,SAAS,EAAC,iBAAiB;kBAACgB,KAAK,EAAE;oBAAE0B,eAAe,EAAE;kBAAU;gBAAE;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9EpH,OAAA;kBAAK+G,SAAS,EAAC,iBAAiB;kBAACgB,KAAK,EAAE;oBAAE0B,eAAe,EAAE;kBAAU;gBAAE;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9EpH,OAAA;kBAAK+G,SAAS,EAAC,iBAAiB;kBAACgB,KAAK,EAAE;oBAAE0B,eAAe,EAAE;kBAAU;gBAAE;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC,eACNpH,OAAA;gBAAM+G,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpH,OAAA;YAAK+G,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAEtD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpH,OAAA;UAAK+G,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAC1CrF,WAAW,CAACkI,GAAG,CAAEC,IAAI,iBACpB9J,OAAA;YAEE+G,SAAS,EAAC,iGAAiG;YAC3GgB,KAAK,EAAE;cACL0B,eAAe,EAAErD,mBAAmB,CAAC0D,IAAI,CAAChF,WAAW,CAAC;cACtDiF,SAAS,EAAE,YAAY3D,mBAAmB,CAAC0D,IAAI,CAAChF,WAAW,CAAC;YAC9D,CAAE;YACFqD,OAAO,EAAEA,CAAA,KAAMrH,eAAe,CAACgJ,IAAI,CAAE;YAAA9C,QAAA,gBAErChH,OAAA;cAAK+G,SAAS,EAAC;YAAiD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEpH,OAAA;cAAK+G,SAAS,EAAC;YAAsH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAE5IpH,OAAA;cAAK+G,SAAS,EAAC,gEAAgE;cAAAC,QAAA,gBAC7EhH,OAAA;gBAAM+G,SAAS,EAAC,6EAA6E;gBAAAC,QAAA,EAC1F8C,IAAI,CAAChG;cAAE;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACPpH,OAAA;gBAAM+G,SAAS,EAAC,8EAA8E;gBAAAC,QAAA,GAC3F8C,IAAI,CAAChF,WAAW,CAACwD,OAAO,CAAC,CAAC,CAAC,EAAC,OAC/B;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GAlBD0C,IAAI,CAAChG,EAAE;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBT,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELvG,YAAY,iBACXb,OAAA;UAAK+G,SAAS,EAAC,qDAAqD;UAAAC,QAAA,gBAClEhH,OAAA;YAAK+G,SAAS,EAAC;UAA8C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpEpH,OAAA;YAAI+G,SAAS,EAAC,gDAAgD;YAAAC,QAAA,GAAC,eAChD,EAACnG,YAAY,CAACiD,EAAE;UAAA;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACLpH,OAAA;YAAK+G,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7ChH,OAAA;cAAAgH,QAAA,gBACEhH,OAAA;gBAAM+G,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClEpH,OAAA;gBAAK+G,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,GAC7CnG,YAAY,CAACiE,WAAW,CAACwD,OAAO,CAAC,CAAC,CAAC,EAAC,OACvC;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpH,OAAA;cAAAgH,QAAA,gBACEhH,OAAA;gBAAM+G,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5DpH,OAAA;gBAAK+G,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,GAC7CnG,YAAY,CAACkE,KAAK,CAACuD,OAAO,CAAC,CAAC,CAAC,EAAC,GACjC;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNpH,OAAA;QAAK+G,SAAS,EAAC,+DAA+D;QAAAC,QAAA,gBAC5EhH,OAAA;UAAK+G,SAAS,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjCpH,OAAA;UAAI+G,SAAS,EAAC,uEAAuE;UAAAC,QAAA,EAAC;QAEtF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELpH,OAAA;UAAK+G,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBvF,MAAM,CAACgH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACoB,GAAG,CAAEG,KAAK,IAAK;YACjC,MAAMC,YAAY,GAAGA,CAAA,KAAM;cACzB,QAAQD,KAAK,CAAC1G,IAAI;gBAChB,KAAK,UAAU;kBACb,oBAAOtD,OAAA,CAACP,aAAa;oBAACsH,SAAS,EAAC;kBAAoC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBACzE,KAAK,SAAS;kBACZ,oBAAOpH,OAAA,CAACP,aAAa;oBAACsH,SAAS,EAAC;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAC9D;kBACE,oBAAOpH,OAAA,CAACP,aAAa;oBAACsH,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;cAC9D;YACF,CAAC;YAED,MAAM8C,mBAAmB,GAAGA,CAAA,KAAM;cAChC,QAAQF,KAAK,CAAC1G,IAAI;gBAChB,KAAK,UAAU;kBACb,OAAO,gBAAgB;gBACzB,KAAK,SAAS;kBACZ,OAAO,mBAAmB;gBAC5B;kBACE,OAAO,iBAAiB;cAC5B;YACF,CAAC;YAED,MAAM6G,iBAAiB,GAAGA,CAAA,KAAM;cAC9B,QAAQH,KAAK,CAAC1G,IAAI;gBAChB,KAAK,UAAU;kBACb,OAAO,cAAc;gBACvB,KAAK,SAAS;kBACZ,OAAO,iBAAiB;gBAC1B;kBACE,OAAO,eAAe;cAC1B;YACF,CAAC;YAED,oBACEtD,OAAA;cAEE+G,SAAS,EAAE,eAAemD,mBAAmB,CAAC,CAAC,sFAAuF;cAAAlD,QAAA,gBAEtIhH,OAAA;gBAAK+G,SAAS,EAAC;cAA8C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpEpH,OAAA;gBAAK+G,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDhH,OAAA;kBAAK+G,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,GACzCiD,YAAY,CAAC,CAAC,eACfjK,OAAA;oBAAK+G,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBhH,OAAA;sBAAK+G,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/ChH,OAAA;wBAAM+G,SAAS,EAAE,qBAAqBoD,iBAAiB,CAAC,CAAC,mCAAoC;wBAAAnD,QAAA,EAC1FgD,KAAK,CAAC1G,IAAI,CAAC8G,WAAW,CAAC;sBAAC;wBAAAnD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC,eACPpH,OAAA;wBAAM+G,SAAS,EAAC,sCAAsC;wBAAAC,QAAA,EACnDgD,KAAK,CAACxG;sBAAQ;wBAAAyD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACX,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNpH,OAAA;sBAAK+G,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAClDgD,KAAK,CAACtG;oBAAO;sBAAAuD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNpH,OAAA;kBAAK+G,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBhH,OAAA;oBAAK+G,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,EACvDgD,KAAK,CAACrG;kBAAI;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACNpH,OAAA;oBAAK+G,SAAS,EAAE,qBAAqBiD,KAAK,CAACjG,MAAM,KAAK,QAAQ,GAAG,cAAc,GAAG,gBAAgB,oBAAqB;oBAAAiD,QAAA,EACpHgD,KAAK,CAACjG,MAAM,CAACqG,WAAW,CAAC;kBAAC;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA7BD4C,KAAK,CAAClG,EAAE;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8BV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNpH,OAAA;UAAK+G,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BhH,OAAA;YAAQ+G,SAAS,EAAC,gKAAgK;YAAAC,QAAA,GAAC,mBAChK,EAACvF,MAAM,CAAC8B,MAAM,EAAC,GAClC;UAAA;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpH,OAAA;QAAK+G,SAAS,EAAC,+DAA+D;QAAAC,QAAA,gBAC5EhH,OAAA;UACE+G,SAAS,EAAC,sIAAsI;UAChJoB,OAAO,EAAEA,CAAA,KAAMC,gBAAgB,CAAC,eAAe,CAAE;UACjD5B,KAAK,EAAC,oBAAoB;UAAAQ,QAAA,eAE1BhH,OAAA;YAAK+G,SAAS,EAAC,SAAS;YAACW,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACN,OAAO,EAAC,WAAW;YAAAL,QAAA,eAC5EhH,OAAA;cAAM8H,aAAa,EAAC,OAAO;cAACO,cAAc,EAAC,OAAO;cAACT,WAAW,EAAE,CAAE;cAACH,CAAC,EAAC;YAA2F;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACTpH,OAAA;UAAK+G,SAAS,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjCpH,OAAA;UAAI+G,SAAS,EAAC,uEAAuE;UAAAC,QAAA,EAAC;QAEtF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELpH,OAAA;UAAK+G,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExBhH,OAAA;YAAK+G,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBAClEhH,OAAA;cACE+G,SAAS,EAAC,sIAAsI;cAChJoB,OAAO,EAAEA,CAAA,KAAMC,gBAAgB,CAAC,kBAAkB,CAAE;cACpD5B,KAAK,EAAC,oBAAoB;cAAAQ,QAAA,eAE1BhH,OAAA;gBAAK+G,SAAS,EAAC,SAAS;gBAACW,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACN,OAAO,EAAC,WAAW;gBAAAL,QAAA,eAC5EhH,OAAA;kBAAM8H,aAAa,EAAC,OAAO;kBAACO,cAAc,EAAC,OAAO;kBAACT,WAAW,EAAE,CAAE;kBAACH,CAAC,EAAC;gBAA2F;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACTpH,OAAA;cAAK+G,SAAS,EAAC;YAA8C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpEpH,OAAA;cAAI+G,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpH,OAAA;cAAK+G,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBhH,OAAA,CAACV,mBAAmB;gBAACiJ,KAAK,EAAC,MAAM;gBAACC,MAAM,EAAC,MAAM;gBAAAxB,QAAA,eAC7ChH,OAAA,CAAChB,SAAS;kBAACqF,IAAI,EAAEtC,cAAc,CAAC0G,KAAK,CAAC,CAAC,EAAE,CAAE;kBAAAzB,QAAA,gBACzChH,OAAA,CAACZ,aAAa;oBAACyI,eAAe,EAAC,KAAK;oBAACF,MAAM,EAAC;kBAAwB;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvEpH,OAAA,CAACd,KAAK;oBACJ+J,OAAO,EAAC,MAAM;oBACdC,QAAQ,EAAE,KAAM;oBAChBC,QAAQ,EAAE,KAAM;oBAChBC,IAAI,EAAE;sBAAE1B,IAAI,EAAE,SAAS;sBAAE2B,QAAQ,EAAE,EAAE;sBAAEC,UAAU,EAAE;oBAAQ;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,eACFpH,OAAA,CAACb,KAAK;oBACJoK,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;oBACjBL,QAAQ,EAAE,KAAM;oBAChBC,QAAQ,EAAE,KAAM;oBAChBC,IAAI,EAAE;sBAAE1B,IAAI,EAAE,SAAS;sBAAE2B,QAAQ,EAAE,EAAE;sBAAEC,UAAU,EAAE;oBAAQ;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,eACFpH,OAAA,CAACX,OAAO;oBACNmK,YAAY,EAAE;sBACZC,eAAe,EAAE,sBAAsB;sBACvCC,MAAM,EAAE,kCAAkC;sBAC1CC,YAAY,EAAE,KAAK;sBACnBhD,KAAK,EAAE,SAAS;sBAChB2C,UAAU,EAAE;oBACd;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFpH,OAAA,CAACf,IAAI;oBACHqE,IAAI,EAAC,UAAU;oBACf2F,OAAO,EAAC,aAAa;oBACrBtB,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACfyC,GAAG,EAAE,KAAM;oBACXtC,KAAK,EAAE;sBAAEC,MAAM,EAAE;oBAAgD;kBAAE;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpH,OAAA;YAAK+G,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBAClEhH,OAAA;cACE+G,SAAS,EAAC,sIAAsI;cAChJoB,OAAO,EAAEA,CAAA,KAAMC,gBAAgB,CAAC,iBAAiB,CAAE;cACnD5B,KAAK,EAAC,oBAAoB;cAAAQ,QAAA,eAE1BhH,OAAA;gBAAK+G,SAAS,EAAC,SAAS;gBAACW,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACN,OAAO,EAAC,WAAW;gBAAAL,QAAA,eAC5EhH,OAAA;kBAAM8H,aAAa,EAAC,OAAO;kBAACO,cAAc,EAAC,OAAO;kBAACT,WAAW,EAAE,CAAE;kBAACH,CAAC,EAAC;gBAA2F;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACTpH,OAAA;cAAK+G,SAAS,EAAC;YAA8C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpEpH,OAAA;cAAI+G,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpH,OAAA;cAAK+G,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDhH,OAAA;gBAAK+G,SAAS,EAAC,iGAAiG;gBAAAC,QAAA,gBAC9GhH,OAAA;kBAAK+G,SAAS,EAAC;gBAA8C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpEpH,OAAA;kBAAK+G,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDhH,OAAA;oBAAM+G,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAA8B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5FpH,OAAA;oBAAM+G,SAAS,EAAC,oDAAoD;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpH,OAAA;gBAAK+G,SAAS,EAAC,iGAAiG;gBAAAC,QAAA,gBAC9GhH,OAAA;kBAAK+G,SAAS,EAAC;gBAA8C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpEpH,OAAA;kBAAK+G,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDhH,OAAA;oBAAM+G,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAA4B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1FpH,OAAA;oBAAM+G,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpH,OAAA;gBAAK+G,SAAS,EAAC,iGAAiG;gBAAAC,QAAA,gBAC9GhH,OAAA;kBAAK+G,SAAS,EAAC;gBAA8C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpEpH,OAAA;kBAAK+G,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDhH,OAAA;oBAAM+G,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAAyB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvFpH,OAAA;oBAAM+G,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpH,OAAA;YAAK+G,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBAClEhH,OAAA;cAAK+G,SAAS,EAAC;YAA8C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpEpH,OAAA;cAAI+G,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpH,OAAA;cAAK+G,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAC5CnF,SAAS,CAAC4G,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACoB,GAAG,CAAEpE,OAAO,IAAK;gBACtC,MAAM6E,kBAAkB,GAAI5E,UAAkB,IAAK;kBACjD,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,cAAc;kBAC3C,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,iBAAiB;kBAC9C,OAAO,gBAAgB;gBACzB,CAAC;gBAED,MAAM6E,mBAAmB,GAAI7E,UAAkB,IAAK;kBAClD,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,gBAAgB;kBAC7C,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,mBAAmB;kBAChD,OAAO,kBAAkB;gBAC3B,CAAC;gBAED,MAAM8E,OAAO,GAAIlH,IAAY,IAAK;kBAChC,QAAQA,IAAI;oBACV,KAAK,OAAO;sBACV,OAAO,GAAG;oBACZ,KAAK,SAAS;sBACZ,OAAO,IAAI;oBACb,KAAK,aAAa;sBAChB,OAAO,KAAK;oBACd;sBACE,OAAO,IAAI;kBACf;gBACF,CAAC;gBAED,oBACEtD,OAAA;kBAEE+G,SAAS,EAAE,eAAewD,mBAAmB,CAAC9E,OAAO,CAACC,UAAU,CAAC,sFAAuF;kBAAAsB,QAAA,gBAExJhH,OAAA;oBAAK+G,SAAS,EAAC;kBAA8C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpEpH,OAAA;oBAAK+G,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDhH,OAAA;sBAAK+G,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,gBAC1ChH,OAAA;wBAAM+G,SAAS,EAAC,SAAS;wBAAAC,QAAA,EAAEwD,OAAO,CAAC/E,OAAO,CAACnC,IAAI;sBAAC;wBAAA2D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACxDpH,OAAA;wBAAAgH,QAAA,gBACEhH,OAAA;0BAAK+G,SAAS,EAAC,sCAAsC;0BAAAC,QAAA,EAAEvB,OAAO,CAACF;wBAAW;0BAAA0B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACjFpH,OAAA;0BAAK+G,SAAS,EAAC,sCAAsC;0BAAAC,QAAA,EAAEvB,OAAO,CAACjC;wBAAQ;0BAAAyD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3E,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNpH,OAAA;sBAAK+G,SAAS,EAAC,YAAY;sBAAAC,QAAA,eACzBhH,OAAA;wBAAK+G,SAAS,EAAE,qBAAqBuD,kBAAkB,CAAC7E,OAAO,CAACC,UAAU,CAAC,oBAAqB;wBAAAsB,QAAA,GAC7FvB,OAAO,CAACC,UAAU,EAAC,GACtB;sBAAA;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAjBD3B,OAAO,CAAC3B,EAAE;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkBZ,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpH,OAAA;YAAK+G,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBAClEhH,OAAA;cAAK+G,SAAS,EAAC;YAA8C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpEpH,OAAA;cAAI+G,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpH,OAAA;cAAK+G,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7ChH,OAAA;gBAAK+G,SAAS,EAAC,iGAAiG;gBAAAC,QAAA,gBAC9GhH,OAAA;kBAAK+G,SAAS,EAAC;gBAA8C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpEpH,OAAA;kBAAK+G,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDhH,OAAA;oBAAM+G,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAAiC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/FpH,OAAA;oBAAM+G,SAAS,EAAC,oDAAoD;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpH,OAAA;gBAAK+G,SAAS,EAAC,iGAAiG;gBAAAC,QAAA,gBAC9GhH,OAAA;kBAAK+G,SAAS,EAAC;gBAA8C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpEpH,OAAA;kBAAK+G,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDhH,OAAA;oBAAM+G,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAAkC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChGpH,OAAA;oBAAM+G,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpH,OAAA;gBAAK+G,SAAS,EAAC,iGAAiG;gBAAAC,QAAA,gBAC9GhH,OAAA;kBAAK+G,SAAS,EAAC;gBAA8C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpEpH,OAAA;kBAAK+G,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDhH,OAAA;oBAAM+G,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAA+B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7FpH,OAAA;oBAAM+G,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpH,OAAA;QAAK+G,SAAS,EAAC,+DAA+D;QAAAC,QAAA,gBAC5EhH,OAAA;UAAK+G,SAAS,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjCpH,OAAA;UAAK+G,SAAS,EAAC,yDAAyD;UAAAC,QAAA,gBACtEhH,OAAA;YAAK+G,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC3ChH,OAAA;cAAM+G,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3EpH,OAAA;cAAK+G,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAEtG,WAAW,CAACkD,kBAAkB,CAAC;YAAC;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CAAC,eACNpH,OAAA;YAAK+G,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ChH,OAAA;cAAK+G,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1ChH,OAAA;gBAAK+G,SAAS,EAAC;cAA4D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClFpH,OAAA;gBAAM+G,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,eACNpH,OAAA;cAAK+G,SAAS,EAAC,8BAA8B;cAAAC,QAAA,gBAC3ChH,OAAA;gBAAM+G,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtEpH,OAAA;gBAAK+G,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNpH,OAAA;cAAK+G,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1ChH,OAAA,CAACF,GAAG;gBAACiH,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvDpH,OAAA;gBAAM+G,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC5ChH,OAAA;kBAAM+G,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvEpH,OAAA;kBAAK+G,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAAErF,WAAW,CAAC4B;gBAAM;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3G,EAAA,CA3yBID,0BAA0B;AAAAiK,EAAA,GAA1BjK,0BAA0B;AA6yBhC,eAAeA,0BAA0B;AAAC,IAAAiK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}