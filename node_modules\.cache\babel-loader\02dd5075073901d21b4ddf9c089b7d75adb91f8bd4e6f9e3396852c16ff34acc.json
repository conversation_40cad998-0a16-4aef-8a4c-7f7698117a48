{"ast": null, "code": "import { timeInterval } from \"./interval.js\";\nexport const millisecond = timeInterval(() => {\n  // noop\n}, (date, step) => {\n  date.setTime(+date + step);\n}, (start, end) => {\n  return end - start;\n});\n\n// An optimized implementation for this simple case.\nmillisecond.every = k => {\n  k = Math.floor(k);\n  if (!isFinite(k) || !(k > 0)) return null;\n  if (!(k > 1)) return millisecond;\n  return timeInterval(date => {\n    date.setTime(Math.floor(date / k) * k);\n  }, (date, step) => {\n    date.setTime(+date + step * k);\n  }, (start, end) => {\n    return (end - start) / k;\n  });\n};\nexport const milliseconds = millisecond.range;", "map": {"version": 3, "names": ["timeInterval", "millisecond", "date", "step", "setTime", "start", "end", "every", "k", "Math", "floor", "isFinite", "milliseconds", "range"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/node_modules/d3-time/src/millisecond.js"], "sourcesContent": ["import {timeInterval} from \"./interval.js\";\n\nexport const millisecond = timeInterval(() => {\n  // noop\n}, (date, step) => {\n  date.setTime(+date + step);\n}, (start, end) => {\n  return end - start;\n});\n\n// An optimized implementation for this simple case.\nmillisecond.every = (k) => {\n  k = Math.floor(k);\n  if (!isFinite(k) || !(k > 0)) return null;\n  if (!(k > 1)) return millisecond;\n  return timeInterval((date) => {\n    date.setTime(Math.floor(date / k) * k);\n  }, (date, step) => {\n    date.setTime(+date + step * k);\n  }, (start, end) => {\n    return (end - start) / k;\n  });\n};\n\nexport const milliseconds = millisecond.range;\n"], "mappings": "AAAA,SAAQA,YAAY,QAAO,eAAe;AAE1C,OAAO,MAAMC,WAAW,GAAGD,YAAY,CAAC,MAAM;EAC5C;AAAA,CACD,EAAE,CAACE,IAAI,EAAEC,IAAI,KAAK;EACjBD,IAAI,CAACE,OAAO,CAAC,CAACF,IAAI,GAAGC,IAAI,CAAC;AAC5B,CAAC,EAAE,CAACE,KAAK,EAAEC,GAAG,KAAK;EACjB,OAAOA,GAAG,GAAGD,KAAK;AACpB,CAAC,CAAC;;AAEF;AACAJ,WAAW,CAACM,KAAK,GAAIC,CAAC,IAAK;EACzBA,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACF,CAAC,CAAC;EACjB,IAAI,CAACG,QAAQ,CAACH,CAAC,CAAC,IAAI,EAAEA,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,IAAI;EACzC,IAAI,EAAEA,CAAC,GAAG,CAAC,CAAC,EAAE,OAAOP,WAAW;EAChC,OAAOD,YAAY,CAAEE,IAAI,IAAK;IAC5BA,IAAI,CAACE,OAAO,CAACK,IAAI,CAACC,KAAK,CAACR,IAAI,GAAGM,CAAC,CAAC,GAAGA,CAAC,CAAC;EACxC,CAAC,EAAE,CAACN,IAAI,EAAEC,IAAI,KAAK;IACjBD,IAAI,CAACE,OAAO,CAAC,CAACF,IAAI,GAAGC,IAAI,GAAGK,CAAC,CAAC;EAChC,CAAC,EAAE,CAACH,KAAK,EAAEC,GAAG,KAAK;IACjB,OAAO,CAACA,GAAG,GAAGD,KAAK,IAAIG,CAAC;EAC1B,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMI,YAAY,GAAGX,WAAW,CAACY,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}