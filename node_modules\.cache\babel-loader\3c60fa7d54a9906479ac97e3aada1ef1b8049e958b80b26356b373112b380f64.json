{"ast": null, "code": "var ListCache = require('./_ListCache'),\n  Map = require('./_Map'),\n  MapCache = require('./_MapCache');\n\n/** Used as the size to enable large array optimizations. */\nvar LARGE_ARRAY_SIZE = 200;\n\n/**\n * Sets the stack `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Stack\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the stack cache instance.\n */\nfunction stackSet(key, value) {\n  var data = this.__data__;\n  if (data instanceof ListCache) {\n    var pairs = data.__data__;\n    if (!Map || pairs.length < LARGE_ARRAY_SIZE - 1) {\n      pairs.push([key, value]);\n      this.size = ++data.size;\n      return this;\n    }\n    data = this.__data__ = new MapCache(pairs);\n  }\n  data.set(key, value);\n  this.size = data.size;\n  return this;\n}\nmodule.exports = stackSet;", "map": {"version": 3, "names": ["ListCache", "require", "Map", "MapCache", "LARGE_ARRAY_SIZE", "stackSet", "key", "value", "data", "__data__", "pairs", "length", "push", "size", "set", "module", "exports"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/node_modules/lodash/_stackSet.js"], "sourcesContent": ["var ListCache = require('./_ListCache'),\n    Map = require('./_Map'),\n    MapCache = require('./_MapCache');\n\n/** Used as the size to enable large array optimizations. */\nvar LARGE_ARRAY_SIZE = 200;\n\n/**\n * Sets the stack `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Stack\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the stack cache instance.\n */\nfunction stackSet(key, value) {\n  var data = this.__data__;\n  if (data instanceof ListCache) {\n    var pairs = data.__data__;\n    if (!Map || (pairs.length < LARGE_ARRAY_SIZE - 1)) {\n      pairs.push([key, value]);\n      this.size = ++data.size;\n      return this;\n    }\n    data = this.__data__ = new MapCache(pairs);\n  }\n  data.set(key, value);\n  this.size = data.size;\n  return this;\n}\n\nmodule.exports = stackSet;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;EACnCC,GAAG,GAAGD,OAAO,CAAC,QAAQ,CAAC;EACvBE,QAAQ,GAAGF,OAAO,CAAC,aAAa,CAAC;;AAErC;AACA,IAAIG,gBAAgB,GAAG,GAAG;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,GAAG,EAAEC,KAAK,EAAE;EAC5B,IAAIC,IAAI,GAAG,IAAI,CAACC,QAAQ;EACxB,IAAID,IAAI,YAAYR,SAAS,EAAE;IAC7B,IAAIU,KAAK,GAAGF,IAAI,CAACC,QAAQ;IACzB,IAAI,CAACP,GAAG,IAAKQ,KAAK,CAACC,MAAM,GAAGP,gBAAgB,GAAG,CAAE,EAAE;MACjDM,KAAK,CAACE,IAAI,CAAC,CAACN,GAAG,EAAEC,KAAK,CAAC,CAAC;MACxB,IAAI,CAACM,IAAI,GAAG,EAAEL,IAAI,CAACK,IAAI;MACvB,OAAO,IAAI;IACb;IACAL,IAAI,GAAG,IAAI,CAACC,QAAQ,GAAG,IAAIN,QAAQ,CAACO,KAAK,CAAC;EAC5C;EACAF,IAAI,CAACM,GAAG,CAACR,GAAG,EAAEC,KAAK,CAAC;EACpB,IAAI,CAACM,IAAI,GAAGL,IAAI,CAACK,IAAI;EACrB,OAAO,IAAI;AACb;AAEAE,MAAM,CAACC,OAAO,GAAGX,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}