{"ast": null, "code": "var _jsxFileName = \"D:\\\\00-WKYap\\\\12-AIApps-AgumentCode\\\\01-Unified IPS Dashboard\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport './App.css';\nimport ThemeSelector from './components/ThemeSelector.tsx';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: /*#__PURE__*/_jsxDEV(ThemeSelector, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "ThemeSelector", "jsxDEV", "_jsxDEV", "App", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport './App.css';\nimport ThemeSelector from './components/ThemeSelector.tsx';\n\nfunction App() {\n  return (\n    <div className=\"App\">\n      <ThemeSelector />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,WAAW;AAClB,OAAOC,aAAa,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAKE,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBH,OAAA,CAACF,aAAa;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AAEV;AAACC,EAAA,GANQP,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}