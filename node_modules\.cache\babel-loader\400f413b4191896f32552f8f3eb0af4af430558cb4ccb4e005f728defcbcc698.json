{"ast": null, "code": "// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.\nexport default function (s) {\n  out: for (var n = s.length, i = 1, i0 = -1, i1; i < n; ++i) {\n    switch (s[i]) {\n      case \".\":\n        i0 = i1 = i;\n        break;\n      case \"0\":\n        if (i0 === 0) i0 = i;\n        i1 = i;\n        break;\n      default:\n        if (!+s[i]) break out;\n        if (i0 > 0) i0 = 0;\n        break;\n    }\n  }\n  return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;\n}", "map": {"version": 3, "names": ["s", "out", "n", "length", "i", "i0", "i1", "slice"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/node_modules/d3-format/src/formatTrim.js"], "sourcesContent": ["// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.\nexport default function(s) {\n  out: for (var n = s.length, i = 1, i0 = -1, i1; i < n; ++i) {\n    switch (s[i]) {\n      case \".\": i0 = i1 = i; break;\n      case \"0\": if (i0 === 0) i0 = i; i1 = i; break;\n      default: if (!+s[i]) break out; if (i0 > 0) i0 = 0; break;\n    }\n  }\n  return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;\n}\n"], "mappings": "AAAA;AACA,eAAe,UAASA,CAAC,EAAE;EACzBC,GAAG,EAAE,KAAK,IAAIC,CAAC,GAAGF,CAAC,CAACG,MAAM,EAAEC,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC,EAAEC,EAAE,EAAEF,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;IAC1D,QAAQJ,CAAC,CAACI,CAAC,CAAC;MACV,KAAK,GAAG;QAAEC,EAAE,GAAGC,EAAE,GAAGF,CAAC;QAAE;MACvB,KAAK,GAAG;QAAE,IAAIC,EAAE,KAAK,CAAC,EAAEA,EAAE,GAAGD,CAAC;QAAEE,EAAE,GAAGF,CAAC;QAAE;MACxC;QAAS,IAAI,CAAC,CAACJ,CAAC,CAACI,CAAC,CAAC,EAAE,MAAMH,GAAG;QAAE,IAAII,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC;QAAE;IACtD;EACF;EACA,OAAOA,EAAE,GAAG,CAAC,GAAGL,CAAC,CAACO,KAAK,CAAC,CAAC,EAAEF,EAAE,CAAC,GAAGL,CAAC,CAACO,KAAK,CAACD,EAAE,GAAG,CAAC,CAAC,GAAGN,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}