{"ast": null, "code": "var arrayMap = require('./_arrayMap'),\n  baseClone = require('./_baseClone'),\n  baseUnset = require('./_baseUnset'),\n  castPath = require('./_castPath'),\n  copyObject = require('./_copyObject'),\n  customOmitClone = require('./_customOmitClone'),\n  flatRest = require('./_flatRest'),\n  getAllKeysIn = require('./_getAllKeysIn');\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1,\n  CLONE_FLAT_FLAG = 2,\n  CLONE_SYMBOLS_FLAG = 4;\n\n/**\n * The opposite of `_.pick`; this method creates an object composed of the\n * own and inherited enumerable property paths of `object` that are not omitted.\n *\n * **Note:** This method is considerably slower than `_.pick`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [paths] The property paths to omit.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.omit(object, ['a', 'c']);\n * // => { 'b': '2' }\n */\nvar omit = flatRest(function (object, paths) {\n  var result = {};\n  if (object == null) {\n    return result;\n  }\n  var isDeep = false;\n  paths = arrayMap(paths, function (path) {\n    path = castPath(path, object);\n    isDeep || (isDeep = path.length > 1);\n    return path;\n  });\n  copyObject(object, getAllKeysIn(object), result);\n  if (isDeep) {\n    result = baseClone(result, CLONE_DEEP_FLAG | CLONE_FLAT_FLAG | CLONE_SYMBOLS_FLAG, customOmitClone);\n  }\n  var length = paths.length;\n  while (length--) {\n    baseUnset(result, paths[length]);\n  }\n  return result;\n});\nmodule.exports = omit;", "map": {"version": 3, "names": ["arrayMap", "require", "baseClone", "baseUnset", "<PERSON><PERSON><PERSON>", "copyObject", "customOmitClone", "flatRest", "getAllKeysIn", "CLONE_DEEP_FLAG", "CLONE_FLAT_FLAG", "CLONE_SYMBOLS_FLAG", "omit", "object", "paths", "result", "isDeep", "path", "length", "module", "exports"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/node_modules/lodash/omit.js"], "sourcesContent": ["var arrayMap = require('./_arrayMap'),\n    baseClone = require('./_baseClone'),\n    baseUnset = require('./_baseUnset'),\n    castPath = require('./_castPath'),\n    copyObject = require('./_copyObject'),\n    customOmitClone = require('./_customOmitClone'),\n    flatRest = require('./_flatRest'),\n    getAllKeysIn = require('./_getAllKeysIn');\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1,\n    CLONE_FLAT_FLAG = 2,\n    CLONE_SYMBOLS_FLAG = 4;\n\n/**\n * The opposite of `_.pick`; this method creates an object composed of the\n * own and inherited enumerable property paths of `object` that are not omitted.\n *\n * **Note:** This method is considerably slower than `_.pick`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [paths] The property paths to omit.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.omit(object, ['a', 'c']);\n * // => { 'b': '2' }\n */\nvar omit = flatRest(function(object, paths) {\n  var result = {};\n  if (object == null) {\n    return result;\n  }\n  var isDeep = false;\n  paths = arrayMap(paths, function(path) {\n    path = castPath(path, object);\n    isDeep || (isDeep = path.length > 1);\n    return path;\n  });\n  copyObject(object, getAllKeysIn(object), result);\n  if (isDeep) {\n    result = baseClone(result, CLONE_DEEP_FLAG | CLONE_FLAT_FLAG | CLONE_SYMBOLS_FLAG, customOmitClone);\n  }\n  var length = paths.length;\n  while (length--) {\n    baseUnset(result, paths[length]);\n  }\n  return result;\n});\n\nmodule.exports = omit;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;EACjCC,SAAS,GAAGD,OAAO,CAAC,cAAc,CAAC;EACnCE,SAAS,GAAGF,OAAO,CAAC,cAAc,CAAC;EACnCG,QAAQ,GAAGH,OAAO,CAAC,aAAa,CAAC;EACjCI,UAAU,GAAGJ,OAAO,CAAC,eAAe,CAAC;EACrCK,eAAe,GAAGL,OAAO,CAAC,oBAAoB,CAAC;EAC/CM,QAAQ,GAAGN,OAAO,CAAC,aAAa,CAAC;EACjCO,YAAY,GAAGP,OAAO,CAAC,iBAAiB,CAAC;;AAE7C;AACA,IAAIQ,eAAe,GAAG,CAAC;EACnBC,eAAe,GAAG,CAAC;EACnBC,kBAAkB,GAAG,CAAC;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,IAAI,GAAGL,QAAQ,CAAC,UAASM,MAAM,EAAEC,KAAK,EAAE;EAC1C,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAIF,MAAM,IAAI,IAAI,EAAE;IAClB,OAAOE,MAAM;EACf;EACA,IAAIC,MAAM,GAAG,KAAK;EAClBF,KAAK,GAAGd,QAAQ,CAACc,KAAK,EAAE,UAASG,IAAI,EAAE;IACrCA,IAAI,GAAGb,QAAQ,CAACa,IAAI,EAAEJ,MAAM,CAAC;IAC7BG,MAAM,KAAKA,MAAM,GAAGC,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IACpC,OAAOD,IAAI;EACb,CAAC,CAAC;EACFZ,UAAU,CAACQ,MAAM,EAAEL,YAAY,CAACK,MAAM,CAAC,EAAEE,MAAM,CAAC;EAChD,IAAIC,MAAM,EAAE;IACVD,MAAM,GAAGb,SAAS,CAACa,MAAM,EAAEN,eAAe,GAAGC,eAAe,GAAGC,kBAAkB,EAAEL,eAAe,CAAC;EACrG;EACA,IAAIY,MAAM,GAAGJ,KAAK,CAACI,MAAM;EACzB,OAAOA,MAAM,EAAE,EAAE;IACff,SAAS,CAACY,MAAM,EAAED,KAAK,CAACI,MAAM,CAAC,CAAC;EAClC;EACA,OAAOH,MAAM;AACf,CAAC,CAAC;AAEFI,MAAM,CAACC,OAAO,GAAGR,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}