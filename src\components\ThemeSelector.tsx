import React, { useState } from 'react';
import UnifiedDataCenterDashboard from './UnifiedDataCenterDashboard';
import GlassmorphismDataCenterDashboard from './GlassmorphismDataCenterDashboard';

const ThemeSelector = () => {
  const [currentTheme, setCurrentTheme] = useState<'futuristic' | 'glassmorphism'>('futuristic');

  const themes = [
    {
      id: 'futuristic',
      name: 'Futuristic Neon',
      description: 'Cyberpunk-inspired theme with neon colors and matrix effects',
      preview: 'linear-gradient(135deg, #001122 0%, #003366 100%)',
      component: UnifiedDataCenterDashboard
    },
    {
      id: 'glassmorphism',
      name: 'Glassmorphism',
      description: 'Modern glass-like transparent design with blur effects',
      preview: 'linear-gradient(135deg, rgba(100, 116, 139, 0.4) 0%, rgba(15, 23, 42, 0.4) 100%)',
      component: GlassmorphismDataCenterDashboard
    }
  ];

  const CurrentComponent = themes.find(theme => theme.id === currentTheme)?.component || UnifiedDataCenterDashboard;

  return (
    <div className="min-h-screen">
      {/* Theme Selector Header */}
      <div className="fixed top-4 left-4 z-50 flex space-x-2">
        {themes.map((theme) => (
          <button
            key={theme.id}
            onClick={() => setCurrentTheme(theme.id as 'futuristic' | 'glassmorphism')}
            className={`px-4 py-2 rounded-lg backdrop-blur-md border transition-all duration-300 ${
              currentTheme === theme.id
                ? 'bg-white bg-opacity-20 border-white border-opacity-40 text-white shadow-lg'
                : 'bg-black bg-opacity-20 border-white border-opacity-20 text-white text-opacity-70 hover:bg-opacity-30'
            }`}
            style={{
              backdropFilter: 'blur(10px)',
              WebkitBackdropFilter: 'blur(10px)'
            }}
          >
            <div className="flex items-center space-x-2">
              <div 
                className="w-4 h-4 rounded-full border border-white border-opacity-30"
                style={{ background: theme.preview }}
              ></div>
              <span className="font-medium text-sm">{theme.name}</span>
            </div>
          </button>
        ))}
      </div>

      {/* Theme Description */}
      <div className="fixed top-16 left-4 z-40 max-w-xs">
        <div 
          className="px-3 py-2 rounded-lg backdrop-blur-md bg-black bg-opacity-20 border border-white border-opacity-20 text-white text-opacity-80"
          style={{
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)'
          }}
        >
          <p className="text-xs">
            {themes.find(theme => theme.id === currentTheme)?.description}
          </p>
        </div>
      </div>

      {/* Background for Glassmorphism Theme */}
      {currentTheme === 'glassmorphism' && (
        <div 
          className="fixed inset-0 z-0"
          style={{
            background: `
              linear-gradient(135deg, 
                rgba(100, 116, 139, 0.4) 0%, 
                rgba(71, 85, 105, 0.4) 25%, 
                rgba(51, 65, 85, 0.4) 50%, 
                rgba(30, 41, 59, 0.4) 75%, 
                rgba(15, 23, 42, 0.4) 100%),
              url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
            `
          }}
        />
      )}

      {/* Dashboard Component */}
      <div className="relative z-10">
        <CurrentComponent />
      </div>
    </div>
  );
};

export default ThemeSelector;
