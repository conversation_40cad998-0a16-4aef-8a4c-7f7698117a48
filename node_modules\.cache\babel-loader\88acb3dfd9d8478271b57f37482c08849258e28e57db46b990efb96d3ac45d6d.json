{"ast": null, "code": "var getNative = require('./_getNative'),\n  root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar Map = getNative(root, 'Map');\nmodule.exports = Map;", "map": {"version": 3, "names": ["getNative", "require", "root", "Map", "module", "exports"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/node_modules/lodash/_Map.js"], "sourcesContent": ["var getNative = require('./_getNative'),\n    root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar Map = getNative(root, 'Map');\n\nmodule.exports = Map;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;EACnCC,IAAI,GAAGD,OAAO,CAAC,SAAS,CAAC;;AAE7B;AACA,IAAIE,GAAG,GAAGH,SAAS,CAACE,IAAI,EAAE,KAAK,CAAC;AAEhCE,MAAM,CAACC,OAAO,GAAGF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}