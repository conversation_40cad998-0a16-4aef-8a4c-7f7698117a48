{"ast": null, "code": "var _jsxFileName = \"D:\\\\00-WKYap\\\\12-AIApps-AgumentCode\\\\01-Unified IPS Dashboard\\\\src\\\\components\\\\UnifiedDataCenterDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';\nimport { AlertTriangle, Bell, Search, User, Menu, Cpu } from 'lucide-react';\n\n// Futuristic CSS styles\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst futuristicStyles = `\n  @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap');\n  \n  .futuristic-bg {\n    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0a0a0a 100%);\n    min-height: 100vh;\n    position: relative;\n    overflow-x: hidden;\n  }\n  \n  .futuristic-bg::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: \n      radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),\n      radial-gradient(circle at 40% 60%, rgba(0, 255, 0, 0.05) 0%, transparent 50%);\n    pointer-events: none;\n  }\n  \n  .neon-border {\n    border: 1px solid rgba(0, 255, 255, 0.3);\n    box-shadow: \n      0 0 10px rgba(0, 255, 255, 0.2),\n      inset 0 0 10px rgba(0, 255, 255, 0.1);\n    background: rgba(0, 20, 40, 0.8);\n    backdrop-filter: blur(10px);\n  }\n  \n  .neon-glow {\n    box-shadow: \n      0 0 20px rgba(0, 255, 255, 0.4),\n      0 0 40px rgba(0, 255, 255, 0.2),\n      inset 0 0 20px rgba(0, 255, 255, 0.1);\n  }\n  \n  .hologram-text {\n    font-family: 'Orbitron', monospace;\n    color: #00ffff;\n    text-shadow: \n      0 0 10px rgba(0, 255, 255, 0.8),\n      0 0 20px rgba(0, 255, 255, 0.4),\n      0 0 30px rgba(0, 255, 255, 0.2);\n  }\n  \n  .data-stream {\n    background: linear-gradient(90deg, transparent 0%, rgba(0, 255, 255, 0.1) 50%, transparent 100%);\n    animation: dataFlow 3s linear infinite;\n  }\n  \n  @keyframes dataFlow {\n    0% { transform: translateX(-100%); }\n    100% { transform: translateX(100%); }\n  }\n  \n  .pulse-glow {\n    animation: pulseGlow 2s ease-in-out infinite alternate;\n  }\n  \n  @keyframes pulseGlow {\n    from { box-shadow: 0 0 20px rgba(0, 255, 255, 0.4); }\n    to { box-shadow: 0 0 30px rgba(0, 255, 255, 0.8), 0 0 40px rgba(0, 255, 255, 0.4); }\n  }\n  \n  .matrix-bg {\n    background: \n      linear-gradient(90deg, transparent 98%, rgba(0, 255, 255, 0.1) 100%),\n      linear-gradient(0deg, transparent 98%, rgba(0, 255, 255, 0.1) 100%);\n    background-size: 20px 20px;\n  }\n  \n  .scan-line {\n    position: relative;\n    overflow: hidden;\n  }\n  \n  .scan-line::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 2px;\n    background: linear-gradient(90deg, transparent, #00ffff, transparent);\n    animation: scanLine 3s linear infinite;\n  }\n  \n  @keyframes scanLine {\n    0% { left: -100%; }\n    100% { left: 100%; }\n  }\n  \n  .custom-scrollbar::-webkit-scrollbar {\n    width: 8px;\n  }\n  \n  .custom-scrollbar::-webkit-scrollbar-track {\n    background: rgba(0, 20, 40, 0.5);\n    border-radius: 4px;\n  }\n  \n  .custom-scrollbar::-webkit-scrollbar-thumb {\n    background: linear-gradient(180deg, #00ffff, #0080ff);\n    border-radius: 4px;\n    box-shadow: 0 0 10px rgba(0, 255, 255, 0.4);\n  }\n  \n  .custom-scrollbar::-webkit-scrollbar-thumb:hover {\n    background: linear-gradient(180deg, #00ffff, #00aaff);\n    box-shadow: 0 0 15px rgba(0, 255, 255, 0.6);\n  }\n  \n  /* Responsive adjustments */\n  @media (max-width: 768px) {\n    .hologram-text {\n      font-size: 0.9em;\n    }\n    \n    .neon-border {\n      padding: 1rem;\n    }\n  }\n`;\n\n// Inject styles\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style');\n  styleElement.textContent = futuristicStyles;\n  document.head.appendChild(styleElement);\n}\nconst UnifiedDataCenterDashboard = () => {\n  _s();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [selectedRack, setSelectedRack] = useState(null);\n  const [kpiData, setKpiData] = useState({\n    pue: 1.45,\n    itLoad: 1.1,\n    coolingLoad: 1.1,\n    trialPower: 2.4,\n    upsPower: 1.11,\n    pduPower: 1.5,\n    totalAlarms: 4,\n    activeAlarms: 2\n  });\n  const [alarms, setAlarms] = useState([]);\n  const [heatmapData, setHeatmapData] = useState([]);\n  const [anomalies, setAnomalies] = useState([]);\n  const [timeSeriesData, setTimeSeriesData] = useState([]);\n\n  // Generate realistic KPI data\n  const generateKPIData = () => {\n    const now = new Date();\n    const hour = now.getHours();\n    const minute = now.getMinutes();\n    const timeVariation = Math.sin((hour * 60 + minute) * Math.PI / 720);\n    return {\n      pue: 1.42 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.02,\n      itLoad: 1.08 + timeVariation * 0.15 + (Math.random() - 0.5) * 0.03,\n      coolingLoad: 1.1 + timeVariation * 0.08 + (Math.random() - 0.5) * 0.02,\n      trialPower: 2.35 + timeVariation * 0.2 + (Math.random() - 0.5) * 0.05,\n      upsPower: 1.1 + (Math.random() - 0.5) * 0.01,\n      pduPower: 1.48 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.02,\n      totalAlarms: Math.floor(3 + Math.random() * 5),\n      activeAlarms: Math.floor(1 + Math.random() * 3)\n    };\n  };\n\n  // Generate alarm data\n  const generateAlarmData = () => {\n    const alarmTypes = ['Critical', 'Warning', 'Info'];\n    const locations = ['Rack A1', 'Rack B3', 'UPS Room', 'Cooling Unit 2', 'Switch Core', 'PDU Zone C'];\n    const messages = {\n      Critical: ['Temperature Threshold Exceeded', 'Power Supply Failure', 'Cooling System Down'],\n      Warning: ['High Humidity Detected', 'UPS Battery Low', 'Network Latency High'],\n      Info: ['Scheduled Maintenance', 'System Update Complete', 'Backup Process Started']\n    };\n    const numAlarms = 3 + Math.floor(Math.random() * 4);\n    const alarms = [];\n    for (let i = 0; i < numAlarms; i++) {\n      const type = alarmTypes[Math.floor(Math.random() * alarmTypes.length)];\n      const location = locations[Math.floor(Math.random() * locations.length)];\n      const messageList = messages[type];\n      const message = messageList[Math.floor(Math.random() * messageList.length)];\n      const time = new Date(Date.now() - Math.random() * 3600000).toLocaleTimeString();\n      alarms.push({\n        id: i + 1,\n        type,\n        message,\n        location,\n        time,\n        status: Math.random() > 0.5 ? 'Active' : 'Acknowledged'\n      });\n    }\n    return alarms.sort((a, b) => {\n      const priority = {\n        Critical: 3,\n        Warning: 2,\n        Info: 1\n      };\n      return priority[b.type] - priority[a.type];\n    });\n  };\n\n  // Generate heatmap data\n  const generateHeatmapData = () => {\n    const data = [];\n    const baseTemp = 22;\n    const now = Date.now();\n    for (let row = 0; row < 8; row++) {\n      for (let col = 0; col < 10; col++) {\n        const distanceFromCenter = Math.sqrt(Math.pow(col - 4.5, 2) + Math.pow(row - 3.5, 2));\n        const hotSpotInfluence = Math.sin(now / 10000 + row * col) * 2;\n        const randomVariation = (Math.random() - 0.5) * 3;\n        const temperature = baseTemp + distanceFromCenter * 0.8 + hotSpotInfluence + randomVariation;\n        const power = 60 + Math.random() * 30 + Math.sin(now / 8000 + row + col) * 10;\n        data.push({\n          x: col,\n          y: row,\n          temperature: Math.max(18, Math.min(32, temperature)),\n          power: Math.max(20, Math.min(100, power)),\n          id: `R${row + 1}C${col + 1}`,\n          status: temperature > 28 ? 'critical' : temperature > 25 ? 'warning' : 'normal'\n        });\n      }\n    }\n    return data;\n  };\n\n  // Generate anomaly data\n  const generateAnomalies = () => {\n    const anomalyTypes = [{\n      icon: 'power',\n      description: 'Unusual power consumption pattern'\n    }, {\n      icon: 'temperature',\n      description: 'Temperature gradient anomaly'\n    }, {\n      icon: 'network',\n      description: 'Network bandwidth surge detected'\n    }, {\n      icon: 'cooling',\n      description: 'HVAC performance degradation'\n    }];\n    const locations = ['Rack A3-A5', 'Zone B, Rows 3-4', 'Core switches', 'Cooling Unit 1'];\n    const numAnomalies = 2 + Math.floor(Math.random() * 3);\n    const anomalies = [];\n    for (let i = 0; i < numAnomalies; i++) {\n      const anomaly = anomalyTypes[Math.floor(Math.random() * anomalyTypes.length)];\n      const location = locations[Math.floor(Math.random() * locations.length)];\n      const confidence = 75 + Math.floor(Math.random() * 25);\n      anomalies.push({\n        id: i + 1,\n        ...anomaly,\n        location,\n        confidence,\n        severity: confidence > 90 ? 'critical' : confidence > 80 ? 'warning' : 'info'\n      });\n    }\n    return anomalies.sort((a, b) => b.confidence - a.confidence);\n  };\n\n  // Generate time series data\n  const generateTimeSeriesData = () => {\n    const data = [];\n    const now = new Date();\n    for (let i = 23; i >= 0; i--) {\n      const time = new Date(now.getTime() - i * 60 * 60 * 1000);\n      const hour = time.getHours();\n      const timeVariation = Math.sin(hour * Math.PI / 12);\n      data.push({\n        time: time.toLocaleTimeString('en-US', {\n          hour: '2-digit',\n          minute: '2-digit'\n        }),\n        power: 1.4 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.05,\n        cooling: 1.1 + timeVariation * 0.08 + (Math.random() - 0.5) * 0.03,\n        temperature: 23 + timeVariation * 1.5 + (Math.random() - 0.5) * 0.8,\n        effectiveness: 85 + timeVariation * 5 + (Math.random() - 0.5) * 3\n      });\n    }\n    return data;\n  };\n\n  // Initialize data on component mount\n  useEffect(() => {\n    setKpiData(generateKPIData());\n    setAlarms(generateAlarmData());\n    setHeatmapData(generateHeatmapData());\n    setAnomalies(generateAnomalies());\n    setTimeSeriesData(generateTimeSeriesData());\n  }, []);\n\n  // Update time and KPI data every second\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n      setKpiData(generateKPIData());\n      setTimeSeriesData(generateTimeSeriesData());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Update heatmap every 3 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setHeatmapData(generateHeatmapData());\n    }, 3000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Update alarms every 15 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setAlarms(generateAlarmData());\n    }, 15000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Update anomalies every 20 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setAnomalies(generateAnomalies());\n    }, 20000);\n    return () => clearInterval(interval);\n  }, []);\n  const getTemperatureColor = temp => {\n    if (temp < 20) return '#4ade80';\n    if (temp < 22) return '#84cc16';\n    if (temp < 24) return '#eab308';\n    if (temp < 26) return '#f97316';\n    if (temp < 28) return '#ef4444';\n    return '#dc2626';\n  };\n  const getAlarmColor = type => {\n    switch (type) {\n      case 'Critical':\n        return 'bg-red-50 text-red-700 border-red-200';\n      case 'Warning':\n        return 'bg-yellow-50 text-yellow-700 border-yellow-200';\n      case 'Info':\n        return 'bg-blue-50 text-blue-700 border-blue-200';\n      default:\n        return 'text-gray-600 bg-gray-50';\n    }\n  };\n  const KPIGauge = ({\n    title,\n    value,\n    unit,\n    max,\n    color = 'blue'\n  }) => {\n    const percentage = value / max * 100;\n    const strokeColor = color === 'green' ? '#00ff88' : color === 'red' ? '#ff0044' : '#00ffff';\n    const glowColor = color === 'green' ? 'rgba(0, 255, 136, 0.4)' : color === 'red' ? 'rgba(255, 0, 68, 0.4)' : 'rgba(0, 255, 255, 0.4)';\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"neon-border p-6 rounded-lg relative overflow-hidden matrix-bg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"data-stream absolute top-0 left-0 w-full h-1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-3xl font-bold hologram-text font-['Orbitron']\",\n          children: [value, /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg text-cyan-300\",\n            children: unit\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 20\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-20 h-20 relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-20 h-20 transform -rotate-90\",\n            viewBox: \"0 0 36 36\",\n            children: [/*#__PURE__*/_jsxDEV(\"defs\", {\n              children: /*#__PURE__*/_jsxDEV(\"filter\", {\n                id: `glow-${title}`,\n                children: [/*#__PURE__*/_jsxDEV(\"feGaussianBlur\", {\n                  stdDeviation: \"3\",\n                  result: \"coloredBlur\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"feMerge\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"feMergeNode\", {\n                    in: \"coloredBlur\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"feMergeNode\", {\n                    in: \"SourceGraphic\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\",\n              fill: \"none\",\n              stroke: \"rgba(0, 255, 255, 0.2)\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\",\n              fill: \"none\",\n              stroke: strokeColor,\n              strokeWidth: \"3\",\n              strokeDasharray: `${percentage}, 100`,\n              strokeLinecap: \"round\",\n              style: {\n                filter: `drop-shadow(0 0 10px ${glowColor})`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-bold hologram-text font-['Orbitron']\",\n              children: [Math.round(percentage), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 text-xs text-cyan-500 font-['Exo_2']\",\n        children: [\"MAX: \", max, unit]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"futuristic-bg min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"neon-border backdrop-blur-md px-6 py-4 relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scan-line\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(Menu, {\n              className: \"w-8 h-8 text-cyan-400 pulse-glow cursor-pointer hover:text-cyan-300 transition-colors\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 w-8 h-8 border border-cyan-400 rounded opacity-30 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold hologram-text font-['Orbitron']\",\n              children: \"UNIFIED DATA CENTER\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-cyan-300 font-['Exo_2'] tracking-wider\",\n              children: \"NEURAL COMMAND INTERFACE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative neon-border rounded-lg px-4 py-2\",\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              className: \"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-cyan-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search neural networks...\",\n              className: \"pl-10 pr-4 py-1 bg-transparent text-cyan-300 placeholder-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-400 font-['Exo_2']\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-cyan-300 font-['Exo_2'] font-mono\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-cyan-500\",\n              children: \"SYSTEM TIME\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hologram-text\",\n              children: currentTime.toLocaleTimeString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(Bell, {\n                className: \"w-6 h-6 text-cyan-400 hover:text-cyan-300 transition-colors cursor-pointer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(User, {\n                className: \"w-6 h-6 text-cyan-400 hover:text-cyan-300 transition-colors cursor-pointer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 w-6 h-6 border border-cyan-400 rounded-full animate-ping opacity-20\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 space-y-6 relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"neon-border rounded-lg p-6 relative overflow-hidden matrix-bg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"scan-line\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold mb-6 hologram-text font-['Orbitron'] tracking-wider\",\n            children: \"EXECUTIVE OVERVIEW\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(KPIGauge, {\n              title: \"PUE\",\n              value: kpiData.pue.toFixed(2),\n              unit: \"\",\n              max: 3,\n              color: \"green\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(KPIGauge, {\n              title: \"IT Load\",\n              value: kpiData.itLoad.toFixed(1),\n              unit: \"M\",\n              max: 2,\n              color: \"blue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(KPIGauge, {\n              title: \"Cooling Load\",\n              value: Math.round(kpiData.coolingLoad * 650),\n              unit: \" RT\",\n              max: 1000,\n              color: \"blue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"neon-border p-4 rounded-lg relative overflow-hidden matrix-bg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"data-stream absolute top-0 left-0 w-full h-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-cyan-400 mb-2 font-['Exo_2'] tracking-wider uppercase\",\n                children: \"Alarms\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-red-400 hologram-text font-['Orbitron']\",\n                children: kpiData.totalAlarms\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-cyan-500 font-['Exo_2']\",\n                children: [kpiData.activeAlarms, \" Active\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-2 right-2\",\n                children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                  className: \"w-5 h-5 text-red-400 animate-pulse\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase\",\n              children: \"Thermal Effectiveness Matrix\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-40 neon-border rounded-lg p-4 relative overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"data-stream absolute top-0 left-0 w-full h-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(AreaChart, {\n                  data: timeSeriesData.slice(-12),\n                  children: [/*#__PURE__*/_jsxDEV(\"defs\", {\n                    children: /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n                      id: \"colorEffectiveness\",\n                      x1: \"0\",\n                      y1: \"0\",\n                      x2: \"0\",\n                      y2: \"1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n                        offset: \"5%\",\n                        stopColor: \"#00ffff\",\n                        stopOpacity: 0.8\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 494,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n                        offset: \"95%\",\n                        stopColor: \"#00ffff\",\n                        stopOpacity: 0.1\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 495,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 493,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"2 2\",\n                    stroke: \"rgba(0, 255, 255, 0.2)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"time\",\n                    axisLine: false,\n                    tickLine: false,\n                    tick: {\n                      fill: '#00ffff',\n                      fontSize: 10,\n                      fontFamily: 'Exo 2'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                    domain: [75, 95],\n                    axisLine: false,\n                    tickLine: false,\n                    tick: {\n                      fill: '#00ffff',\n                      fontSize: 10,\n                      fontFamily: 'Exo 2'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    contentStyle: {\n                      backgroundColor: 'rgba(0, 20, 40, 0.9)',\n                      border: '1px solid rgba(0, 255, 255, 0.3)',\n                      borderRadius: '8px',\n                      color: '#00ffff',\n                      fontFamily: 'Exo 2'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 511,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Area, {\n                    type: \"monotone\",\n                    dataKey: \"effectiveness\",\n                    stroke: \"#00ffff\",\n                    strokeWidth: 2,\n                    fillOpacity: 1,\n                    fill: \"url(#colorEffectiveness)\",\n                    style: {\n                      filter: 'drop-shadow(0 0 10px rgba(0, 255, 255, 0.4))'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"neon-border rounded-lg p-6 relative overflow-hidden matrix-bg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"scan-line\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold mb-2 hologram-text font-['Orbitron'] tracking-wider\",\n            children: \"ASSET 360 - THERMAL HEATMAP\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-cyan-400 mb-6 font-['Exo_2'] tracking-wider\",\n            children: \"Data Hall Temperature Distribution\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4 flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-cyan-400 font-['Exo_2'] tracking-wider uppercase\",\n                children: \"Temperature Scale:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-cyan-300 font-['Exo_2']\",\n                  children: \"COOL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-4 h-4 rounded\",\n                    style: {\n                      backgroundColor: '#4ade80'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-4 h-4 rounded\",\n                    style: {\n                      backgroundColor: '#84cc16'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-4 h-4 rounded\",\n                    style: {\n                      backgroundColor: '#eab308'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-4 h-4 rounded\",\n                    style: {\n                      backgroundColor: '#f97316'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-4 h-4 rounded\",\n                    style: {\n                      backgroundColor: '#ef4444'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-4 h-4 rounded\",\n                    style: {\n                      backgroundColor: '#dc2626'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 557,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-cyan-300 font-['Exo_2']\",\n                  children: \"HOT\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-cyan-500 font-['Exo_2']\",\n              children: \"Click rack for details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-10 gap-1 mb-4\",\n            children: heatmapData.map(rack => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"aspect-square rounded cursor-pointer transition-all duration-300 hover:scale-110 relative group\",\n              style: {\n                backgroundColor: getTemperatureColor(rack.temperature),\n                boxShadow: `0 0 10px ${getTemperatureColor(rack.temperature)}40`\n              },\n              onClick: () => setSelectedRack(rack),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-black bg-opacity-20 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-cyan-400 bg-opacity-20 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs font-bold text-white drop-shadow-lg font-['Orbitron']\",\n                  children: rack.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 19\n              }, this)]\n            }, rack.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 13\n          }, this), selectedRack && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"neon-border p-4 rounded-lg relative overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"data-stream absolute top-0 left-0 w-full h-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-bold text-cyan-400 mb-2 font-['Orbitron']\",\n              children: [\"Neural Node: \", selectedRack.id]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-cyan-500 font-['Exo_2']\",\n                  children: \"Temperature:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hologram-text font-['Orbitron']\",\n                  children: [selectedRack.temperature.toFixed(1), \"\\xB0C\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-cyan-500 font-['Exo_2']\",\n                  children: \"Power:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hologram-text font-['Orbitron']\",\n                  children: [selectedRack.power.toFixed(0), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 605,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"neon-border rounded-lg p-6 relative overflow-hidden matrix-bg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"scan-line\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center text-sm relative z-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-cyan-400 font-['Exo_2']\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs tracking-wider uppercase\",\n              children: \"Last Neural Sync:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hologram-text font-['Orbitron'] text-lg\",\n              children: currentTime.toLocaleTimeString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-4 h-4 bg-green-400 rounded-full animate-pulse pulse-glow\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-cyan-300 font-['Exo_2'] tracking-wider\",\n                children: \"ALL SYSTEMS OPERATIONAL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-cyan-400 font-['Exo_2']\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs tracking-wider uppercase\",\n                children: \"Data Stream:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hologram-text font-['Orbitron']\",\n                children: \"LIVE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(Cpu, {\n                className: \"w-5 h-5 text-cyan-400 animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-cyan-300 font-['Exo_2']\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs tracking-wider uppercase\",\n                  children: \"Neural Nodes:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hologram-text font-['Orbitron']\",\n                  children: heatmapData.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 616,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 457,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 409,\n    columnNumber: 5\n  }, this);\n};\n_s(UnifiedDataCenterDashboard, \"Qx0dcFmUGcqvtTYsEDuakExIIZM=\");\n_c = UnifiedDataCenterDashboard;\nexport default UnifiedDataCenterDashboard;\nvar _c;\n$RefreshReg$(_c, \"UnifiedDataCenterDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "AreaChart", "Area", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Bell", "Search", "User", "<PERSON><PERSON>", "Cpu", "jsxDEV", "_jsxDEV", "futuristicStyles", "document", "styleElement", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "UnifiedDataCenterDashboard", "_s", "currentTime", "setCurrentTime", "Date", "selected<PERSON><PERSON>", "setSelectedRack", "kpiData", "setKpiData", "pue", "itLoad", "coolingLoad", "trialPower", "upsPower", "pdu<PERSON>ow<PERSON>", "totalAlarms", "activeAlarms", "alarms", "setAlarms", "heatmapData", "setHeatmapData", "anomalies", "setAnomalies", "timeSeriesData", "setTimeSeriesData", "generateKPIData", "now", "hour", "getHours", "minute", "getMinutes", "timeVariation", "Math", "sin", "PI", "random", "floor", "generateAlarmData", "alarmTypes", "locations", "messages", "Critical", "Warning", "Info", "numAlarms", "i", "type", "length", "location", "messageList", "message", "time", "toLocaleTimeString", "push", "id", "status", "sort", "a", "b", "priority", "generateHeatmapData", "data", "baseTemp", "row", "col", "distanceFromCenter", "sqrt", "pow", "hotSpotInfluence", "randomVariation", "temperature", "power", "x", "y", "max", "min", "generateAnomalies", "anomalyTypes", "icon", "description", "numAnomalies", "anomaly", "confidence", "severity", "generateTimeSeriesData", "getTime", "cooling", "effectiveness", "timer", "setInterval", "clearInterval", "interval", "getTemperatureColor", "temp", "getAlarmColor", "KPIGauge", "title", "value", "unit", "color", "percentage", "strokeColor", "glowColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "viewBox", "stdDeviation", "result", "in", "d", "fill", "stroke", "strokeWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeLinecap", "style", "filter", "round", "placeholder", "toFixed", "width", "height", "slice", "x1", "y1", "x2", "y2", "offset", "stopColor", "stopOpacity", "dataKey", "axisLine", "tickLine", "tick", "fontSize", "fontFamily", "domain", "contentStyle", "backgroundColor", "border", "borderRadius", "fillOpacity", "map", "rack", "boxShadow", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/src/components/UnifiedDataCenterDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON>Chart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';\nimport { AlertTriangle, Thermometer, Zap, Server, Shield, TrendingUp, Settings, Bell, Search, User, Menu, Activity, Cpu, HardDrive, Wifi } from 'lucide-react';\n\n// Futuristic CSS styles\nconst futuristicStyles = `\n  @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap');\n  \n  .futuristic-bg {\n    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0a0a0a 100%);\n    min-height: 100vh;\n    position: relative;\n    overflow-x: hidden;\n  }\n  \n  .futuristic-bg::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: \n      radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),\n      radial-gradient(circle at 40% 60%, rgba(0, 255, 0, 0.05) 0%, transparent 50%);\n    pointer-events: none;\n  }\n  \n  .neon-border {\n    border: 1px solid rgba(0, 255, 255, 0.3);\n    box-shadow: \n      0 0 10px rgba(0, 255, 255, 0.2),\n      inset 0 0 10px rgba(0, 255, 255, 0.1);\n    background: rgba(0, 20, 40, 0.8);\n    backdrop-filter: blur(10px);\n  }\n  \n  .neon-glow {\n    box-shadow: \n      0 0 20px rgba(0, 255, 255, 0.4),\n      0 0 40px rgba(0, 255, 255, 0.2),\n      inset 0 0 20px rgba(0, 255, 255, 0.1);\n  }\n  \n  .hologram-text {\n    font-family: 'Orbitron', monospace;\n    color: #00ffff;\n    text-shadow: \n      0 0 10px rgba(0, 255, 255, 0.8),\n      0 0 20px rgba(0, 255, 255, 0.4),\n      0 0 30px rgba(0, 255, 255, 0.2);\n  }\n  \n  .data-stream {\n    background: linear-gradient(90deg, transparent 0%, rgba(0, 255, 255, 0.1) 50%, transparent 100%);\n    animation: dataFlow 3s linear infinite;\n  }\n  \n  @keyframes dataFlow {\n    0% { transform: translateX(-100%); }\n    100% { transform: translateX(100%); }\n  }\n  \n  .pulse-glow {\n    animation: pulseGlow 2s ease-in-out infinite alternate;\n  }\n  \n  @keyframes pulseGlow {\n    from { box-shadow: 0 0 20px rgba(0, 255, 255, 0.4); }\n    to { box-shadow: 0 0 30px rgba(0, 255, 255, 0.8), 0 0 40px rgba(0, 255, 255, 0.4); }\n  }\n  \n  .matrix-bg {\n    background: \n      linear-gradient(90deg, transparent 98%, rgba(0, 255, 255, 0.1) 100%),\n      linear-gradient(0deg, transparent 98%, rgba(0, 255, 255, 0.1) 100%);\n    background-size: 20px 20px;\n  }\n  \n  .scan-line {\n    position: relative;\n    overflow: hidden;\n  }\n  \n  .scan-line::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 2px;\n    background: linear-gradient(90deg, transparent, #00ffff, transparent);\n    animation: scanLine 3s linear infinite;\n  }\n  \n  @keyframes scanLine {\n    0% { left: -100%; }\n    100% { left: 100%; }\n  }\n  \n  .custom-scrollbar::-webkit-scrollbar {\n    width: 8px;\n  }\n  \n  .custom-scrollbar::-webkit-scrollbar-track {\n    background: rgba(0, 20, 40, 0.5);\n    border-radius: 4px;\n  }\n  \n  .custom-scrollbar::-webkit-scrollbar-thumb {\n    background: linear-gradient(180deg, #00ffff, #0080ff);\n    border-radius: 4px;\n    box-shadow: 0 0 10px rgba(0, 255, 255, 0.4);\n  }\n  \n  .custom-scrollbar::-webkit-scrollbar-thumb:hover {\n    background: linear-gradient(180deg, #00ffff, #00aaff);\n    box-shadow: 0 0 15px rgba(0, 255, 255, 0.6);\n  }\n  \n  /* Responsive adjustments */\n  @media (max-width: 768px) {\n    .hologram-text {\n      font-size: 0.9em;\n    }\n    \n    .neon-border {\n      padding: 1rem;\n    }\n  }\n`;\n\n// Inject styles\nif (typeof document !== 'undefined') {\n  const styleElement = document.createElement('style');\n  styleElement.textContent = futuristicStyles;\n  document.head.appendChild(styleElement);\n}\n\nconst UnifiedDataCenterDashboard = () => {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [selectedRack, setSelectedRack] = useState(null);\n  const [kpiData, setKpiData] = useState({\n    pue: 1.45,\n    itLoad: 1.1,\n    coolingLoad: 1.1,\n    trialPower: 2.4,\n    upsPower: 1.11,\n    pduPower: 1.5,\n    totalAlarms: 4,\n    activeAlarms: 2\n  });\n  const [alarms, setAlarms] = useState([]);\n  const [heatmapData, setHeatmapData] = useState([]);\n  const [anomalies, setAnomalies] = useState([]);\n  const [timeSeriesData, setTimeSeriesData] = useState([]);\n\n  // Generate realistic KPI data\n  const generateKPIData = () => {\n    const now = new Date();\n    const hour = now.getHours();\n    const minute = now.getMinutes();\n    const timeVariation = Math.sin((hour * 60 + minute) * Math.PI / 720);\n\n    return {\n      pue: 1.42 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.02,\n      itLoad: 1.08 + timeVariation * 0.15 + (Math.random() - 0.5) * 0.03,\n      coolingLoad: 1.1 + timeVariation * 0.08 + (Math.random() - 0.5) * 0.02,\n      trialPower: 2.35 + timeVariation * 0.2 + (Math.random() - 0.5) * 0.05,\n      upsPower: 1.1 + (Math.random() - 0.5) * 0.01,\n      pduPower: 1.48 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.02,\n      totalAlarms: Math.floor(3 + Math.random() * 5),\n      activeAlarms: Math.floor(1 + Math.random() * 3)\n    };\n  };\n\n  // Generate alarm data\n  const generateAlarmData = () => {\n    const alarmTypes = ['Critical', 'Warning', 'Info'];\n    const locations = ['Rack A1', 'Rack B3', 'UPS Room', 'Cooling Unit 2', 'Switch Core', 'PDU Zone C'];\n    const messages = {\n      Critical: ['Temperature Threshold Exceeded', 'Power Supply Failure', 'Cooling System Down'],\n      Warning: ['High Humidity Detected', 'UPS Battery Low', 'Network Latency High'],\n      Info: ['Scheduled Maintenance', 'System Update Complete', 'Backup Process Started']\n    };\n\n    const numAlarms = 3 + Math.floor(Math.random() * 4);\n    const alarms = [];\n\n    for (let i = 0; i < numAlarms; i++) {\n      const type = alarmTypes[Math.floor(Math.random() * alarmTypes.length)];\n      const location = locations[Math.floor(Math.random() * locations.length)];\n      const messageList = messages[type];\n      const message = messageList[Math.floor(Math.random() * messageList.length)];\n      const time = new Date(Date.now() - Math.random() * 3600000).toLocaleTimeString();\n\n      alarms.push({\n        id: i + 1,\n        type,\n        message,\n        location,\n        time,\n        status: Math.random() > 0.5 ? 'Active' : 'Acknowledged'\n      });\n    }\n\n    return alarms.sort((a, b) => {\n      const priority = { Critical: 3, Warning: 2, Info: 1 };\n      return priority[b.type] - priority[a.type];\n    });\n  };\n\n  // Generate heatmap data\n  const generateHeatmapData = () => {\n    const data = [];\n    const baseTemp = 22;\n    const now = Date.now();\n\n    for (let row = 0; row < 8; row++) {\n      for (let col = 0; col < 10; col++) {\n        const distanceFromCenter = Math.sqrt(Math.pow(col - 4.5, 2) + Math.pow(row - 3.5, 2));\n        const hotSpotInfluence = Math.sin(now / 10000 + row * col) * 2;\n        const randomVariation = (Math.random() - 0.5) * 3;\n        const temperature = baseTemp + distanceFromCenter * 0.8 + hotSpotInfluence + randomVariation;\n        const power = 60 + Math.random() * 30 + Math.sin(now / 8000 + row + col) * 10;\n\n        data.push({\n          x: col,\n          y: row,\n          temperature: Math.max(18, Math.min(32, temperature)),\n          power: Math.max(20, Math.min(100, power)),\n          id: `R${row + 1}C${col + 1}`,\n          status: temperature > 28 ? 'critical' : temperature > 25 ? 'warning' : 'normal'\n        });\n      }\n    }\n\n    return data;\n  };\n\n  // Generate anomaly data\n  const generateAnomalies = () => {\n    const anomalyTypes = [\n      { icon: 'power', description: 'Unusual power consumption pattern' },\n      { icon: 'temperature', description: 'Temperature gradient anomaly' },\n      { icon: 'network', description: 'Network bandwidth surge detected' },\n      { icon: 'cooling', description: 'HVAC performance degradation' }\n    ];\n\n    const locations = ['Rack A3-A5', 'Zone B, Rows 3-4', 'Core switches', 'Cooling Unit 1'];\n    const numAnomalies = 2 + Math.floor(Math.random() * 3);\n    const anomalies = [];\n\n    for (let i = 0; i < numAnomalies; i++) {\n      const anomaly = anomalyTypes[Math.floor(Math.random() * anomalyTypes.length)];\n      const location = locations[Math.floor(Math.random() * locations.length)];\n      const confidence = 75 + Math.floor(Math.random() * 25);\n\n      anomalies.push({\n        id: i + 1,\n        ...anomaly,\n        location,\n        confidence,\n        severity: confidence > 90 ? 'critical' : confidence > 80 ? 'warning' : 'info'\n      });\n    }\n\n    return anomalies.sort((a, b) => b.confidence - a.confidence);\n  };\n\n  // Generate time series data\n  const generateTimeSeriesData = () => {\n    const data = [];\n    const now = new Date();\n\n    for (let i = 23; i >= 0; i--) {\n      const time = new Date(now.getTime() - i * 60 * 60 * 1000);\n      const hour = time.getHours();\n      const timeVariation = Math.sin((hour * Math.PI) / 12);\n\n      data.push({\n        time: time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),\n        power: 1.4 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.05,\n        cooling: 1.1 + timeVariation * 0.08 + (Math.random() - 0.5) * 0.03,\n        temperature: 23 + timeVariation * 1.5 + (Math.random() - 0.5) * 0.8,\n        effectiveness: 85 + timeVariation * 5 + (Math.random() - 0.5) * 3\n      });\n    }\n\n    return data;\n  };\n\n  // Initialize data on component mount\n  useEffect(() => {\n    setKpiData(generateKPIData());\n    setAlarms(generateAlarmData());\n    setHeatmapData(generateHeatmapData());\n    setAnomalies(generateAnomalies());\n    setTimeSeriesData(generateTimeSeriesData());\n  }, []);\n\n  // Update time and KPI data every second\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n      setKpiData(generateKPIData());\n      setTimeSeriesData(generateTimeSeriesData());\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, []);\n\n  // Update heatmap every 3 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setHeatmapData(generateHeatmapData());\n    }, 3000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Update alarms every 15 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setAlarms(generateAlarmData());\n    }, 15000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Update anomalies every 20 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setAnomalies(generateAnomalies());\n    }, 20000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const getTemperatureColor = (temp) => {\n    if (temp < 20) return '#4ade80';\n    if (temp < 22) return '#84cc16';\n    if (temp < 24) return '#eab308';\n    if (temp < 26) return '#f97316';\n    if (temp < 28) return '#ef4444';\n    return '#dc2626';\n  };\n\n  const getAlarmColor = (type) => {\n    switch (type) {\n      case 'Critical': return 'bg-red-50 text-red-700 border-red-200';\n      case 'Warning': return 'bg-yellow-50 text-yellow-700 border-yellow-200';\n      case 'Info': return 'bg-blue-50 text-blue-700 border-blue-200';\n      default: return 'text-gray-600 bg-gray-50';\n    }\n  };\n\n  const KPIGauge = ({ title, value, unit, max, color = 'blue' }) => {\n    const percentage = (value / max) * 100;\n    const strokeColor = color === 'green' ? '#00ff88' : color === 'red' ? '#ff0044' : '#00ffff';\n    const glowColor = color === 'green' ? 'rgba(0, 255, 136, 0.4)' : color === 'red' ? 'rgba(255, 0, 68, 0.4)' : 'rgba(0, 255, 255, 0.4)';\n\n    return (\n      <div className=\"neon-border p-6 rounded-lg relative overflow-hidden matrix-bg\">\n        <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n        <div className=\"text-sm text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase\">{title}</div>\n        <div className=\"flex items-center justify-between\">\n          <div className=\"text-3xl font-bold hologram-text font-['Orbitron']\">\n            {value}<span className=\"text-lg text-cyan-300\">{unit}</span>\n          </div>\n          <div className=\"w-20 h-20 relative\">\n            <svg className=\"w-20 h-20 transform -rotate-90\" viewBox=\"0 0 36 36\">\n              <defs>\n                <filter id={`glow-${title}`}>\n                  <feGaussianBlur stdDeviation=\"3\" result=\"coloredBlur\"/>\n                  <feMerge>\n                    <feMergeNode in=\"coloredBlur\"/>\n                    <feMergeNode in=\"SourceGraphic\"/>\n                  </feMerge>\n                </filter>\n              </defs>\n              <path\n                d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                fill=\"none\"\n                stroke=\"rgba(0, 255, 255, 0.2)\"\n                strokeWidth=\"2\"\n              />\n              <path\n                d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                fill=\"none\"\n                stroke={strokeColor}\n                strokeWidth=\"3\"\n                strokeDasharray={`${percentage}, 100`}\n                strokeLinecap=\"round\"\n                style={{ filter: `drop-shadow(0 0 10px ${glowColor})` }}\n              />\n            </svg>\n            <div className=\"absolute inset-0 flex items-center justify-center\">\n              <span className=\"text-sm font-bold hologram-text font-['Orbitron']\">{Math.round(percentage)}%</span>\n            </div>\n          </div>\n        </div>\n        <div className=\"mt-2 text-xs text-cyan-500 font-['Exo_2']\">\n          MAX: {max}{unit}\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"futuristic-bg min-h-screen\">\n      {/* Header */}\n      <header className=\"neon-border backdrop-blur-md px-6 py-4 relative\">\n        <div className=\"scan-line\"></div>\n        <div className=\"flex items-center justify-between relative z-10\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"relative\">\n              <Menu className=\"w-8 h-8 text-cyan-400 pulse-glow cursor-pointer hover:text-cyan-300 transition-colors\" />\n              <div className=\"absolute inset-0 w-8 h-8 border border-cyan-400 rounded opacity-30 animate-pulse\"></div>\n            </div>\n            <div>\n              <h1 className=\"text-2xl font-bold hologram-text font-['Orbitron']\">\n                UNIFIED DATA CENTER\n              </h1>\n              <div className=\"text-sm text-cyan-300 font-['Exo_2'] tracking-wider\">\n                NEURAL COMMAND INTERFACE\n              </div>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-6\">\n            <div className=\"relative neon-border rounded-lg px-4 py-2\">\n              <Search className=\"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-cyan-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search neural networks...\"\n                className=\"pl-10 pr-4 py-1 bg-transparent text-cyan-300 placeholder-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-400 font-['Exo_2']\"\n              />\n            </div>\n            <div className=\"text-sm text-cyan-300 font-['Exo_2'] font-mono\">\n              <div className=\"text-xs text-cyan-500\">SYSTEM TIME</div>\n              <div className=\"hologram-text\">\n                {currentTime.toLocaleTimeString()}\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"relative\">\n                <Bell className=\"w-6 h-6 text-cyan-400 hover:text-cyan-300 transition-colors cursor-pointer\" />\n                <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse\"></div>\n              </div>\n              <div className=\"relative\">\n                <User className=\"w-6 h-6 text-cyan-400 hover:text-cyan-300 transition-colors cursor-pointer\" />\n                <div className=\"absolute inset-0 w-6 h-6 border border-cyan-400 rounded-full animate-ping opacity-20\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"p-6 space-y-6 relative\">\n        {/* Top Row - Executive Overview & Energy Console */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Executive Overview */}\n          <div className=\"neon-border rounded-lg p-6 relative overflow-hidden matrix-bg\">\n            <div className=\"scan-line\"></div>\n            <h2 className=\"text-xl font-bold mb-6 hologram-text font-['Orbitron'] tracking-wider\">\n              EXECUTIVE OVERVIEW\n            </h2>\n\n            {/* KPI Cards */}\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n              <KPIGauge title=\"PUE\" value={kpiData.pue.toFixed(2)} unit=\"\" max={3} color=\"green\" />\n              <KPIGauge title=\"IT Load\" value={kpiData.itLoad.toFixed(1)} unit=\"M\" max={2} color=\"blue\" />\n              <KPIGauge title=\"Cooling Load\" value={Math.round(kpiData.coolingLoad * 650)} unit=\" RT\" max={1000} color=\"blue\" />\n              <div className=\"neon-border p-4 rounded-lg relative overflow-hidden matrix-bg\">\n                <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n                <div className=\"text-sm text-cyan-400 mb-2 font-['Exo_2'] tracking-wider uppercase\">Alarms</div>\n                <div className=\"text-2xl font-bold text-red-400 hologram-text font-['Orbitron']\">{kpiData.totalAlarms}</div>\n                <div className=\"text-xs text-cyan-500 font-['Exo_2']\">{kpiData.activeAlarms} Active</div>\n                <div className=\"absolute top-2 right-2\">\n                  <AlertTriangle className=\"w-5 h-5 text-red-400 animate-pulse\" />\n                </div>\n              </div>\n            </div>\n\n            {/* Thermal Effectiveness Chart */}\n            <div className=\"mb-6\">\n              <h3 className=\"text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase\">\n                Thermal Effectiveness Matrix\n              </h3>\n              <div className=\"h-40 neon-border rounded-lg p-4 relative overflow-hidden\">\n                <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <AreaChart data={timeSeriesData.slice(-12)}>\n                    <defs>\n                      <linearGradient id=\"colorEffectiveness\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"1\">\n                        <stop offset=\"5%\" stopColor=\"#00ffff\" stopOpacity={0.8}/>\n                        <stop offset=\"95%\" stopColor=\"#00ffff\" stopOpacity={0.1}/>\n                      </linearGradient>\n                    </defs>\n                    <CartesianGrid strokeDasharray=\"2 2\" stroke=\"rgba(0, 255, 255, 0.2)\" />\n                    <XAxis\n                      dataKey=\"time\"\n                      axisLine={false}\n                      tickLine={false}\n                      tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}\n                    />\n                    <YAxis\n                      domain={[75, 95]}\n                      axisLine={false}\n                      tickLine={false}\n                      tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}\n                    />\n                    <Tooltip\n                      contentStyle={{\n                        backgroundColor: 'rgba(0, 20, 40, 0.9)',\n                        border: '1px solid rgba(0, 255, 255, 0.3)',\n                        borderRadius: '8px',\n                        color: '#00ffff',\n                        fontFamily: 'Exo 2'\n                      }}\n                    />\n                    <Area\n                      type=\"monotone\"\n                      dataKey=\"effectiveness\"\n                      stroke=\"#00ffff\"\n                      strokeWidth={2}\n                      fillOpacity={1}\n                      fill=\"url(#colorEffectiveness)\"\n                      style={{ filter: 'drop-shadow(0 0 10px rgba(0, 255, 255, 0.4))' }}\n                    />\n                  </AreaChart>\n                </ResponsiveContainer>\n              </div>\n            </div>\n          </div>\n\n          {/* Asset 360 - Thermal Heatmap */}\n          <div className=\"neon-border rounded-lg p-6 relative overflow-hidden matrix-bg\">\n            <div className=\"scan-line\"></div>\n            <h2 className=\"text-xl font-bold mb-2 hologram-text font-['Orbitron'] tracking-wider\">\n              ASSET 360 - THERMAL HEATMAP\n            </h2>\n            <p className=\"text-sm text-cyan-400 mb-6 font-['Exo_2'] tracking-wider\">\n              Data Hall Temperature Distribution\n            </p>\n\n            {/* Color Legend */}\n            <div className=\"mb-4 flex items-center justify-between\">\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"text-xs text-cyan-400 font-['Exo_2'] tracking-wider uppercase\">Temperature Scale:</span>\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-xs text-cyan-300 font-['Exo_2']\">COOL</span>\n                  <div className=\"flex space-x-1\">\n                    <div className=\"w-4 h-4 rounded\" style={{ backgroundColor: '#4ade80' }}></div>\n                    <div className=\"w-4 h-4 rounded\" style={{ backgroundColor: '#84cc16' }}></div>\n                    <div className=\"w-4 h-4 rounded\" style={{ backgroundColor: '#eab308' }}></div>\n                    <div className=\"w-4 h-4 rounded\" style={{ backgroundColor: '#f97316' }}></div>\n                    <div className=\"w-4 h-4 rounded\" style={{ backgroundColor: '#ef4444' }}></div>\n                    <div className=\"w-4 h-4 rounded\" style={{ backgroundColor: '#dc2626' }}></div>\n                  </div>\n                  <span className=\"text-xs text-cyan-300 font-['Exo_2']\">HOT</span>\n                </div>\n              </div>\n              <div className=\"text-xs text-cyan-500 font-['Exo_2']\">\n                Click rack for details\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-10 gap-1 mb-4\">\n              {heatmapData.map((rack) => (\n                <div\n                  key={rack.id}\n                  className=\"aspect-square rounded cursor-pointer transition-all duration-300 hover:scale-110 relative group\"\n                  style={{\n                    backgroundColor: getTemperatureColor(rack.temperature),\n                    boxShadow: `0 0 10px ${getTemperatureColor(rack.temperature)}40`\n                  }}\n                  onClick={() => setSelectedRack(rack)}\n                >\n                  <div className=\"absolute inset-0 bg-black bg-opacity-20 rounded\"></div>\n                  <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-cyan-400 bg-opacity-20 rounded\"></div>\n                  {/* Rack Number Display */}\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <span className=\"text-xs font-bold text-white drop-shadow-lg font-['Orbitron']\">\n                      {rack.id}\n                    </span>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {selectedRack && (\n              <div className=\"neon-border p-4 rounded-lg relative overflow-hidden\">\n                <div className=\"data-stream absolute top-0 left-0 w-full h-1\"></div>\n                <h4 className=\"font-bold text-cyan-400 mb-2 font-['Orbitron']\">\n                  Neural Node: {selectedRack.id}\n                </h4>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-cyan-500 font-['Exo_2']\">Temperature:</span>\n                    <div className=\"hologram-text font-['Orbitron']\">\n                      {selectedRack.temperature.toFixed(1)}°C\n                    </div>\n                  </div>\n                  <div>\n                    <span className=\"text-cyan-500 font-['Exo_2']\">Power:</span>\n                    <div className=\"hologram-text font-['Orbitron']\">\n                      {selectedRack.power.toFixed(0)}%\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Status Footer */}\n        <div className=\"neon-border rounded-lg p-6 relative overflow-hidden matrix-bg\">\n          <div className=\"scan-line\"></div>\n          <div className=\"flex justify-between items-center text-sm relative z-10\">\n            <div className=\"text-cyan-400 font-['Exo_2']\">\n              <span className=\"text-xs tracking-wider uppercase\">Last Neural Sync:</span>\n              <div className=\"hologram-text font-['Orbitron'] text-lg\">{currentTime.toLocaleTimeString()}</div>\n            </div>\n            <div className=\"flex items-center space-x-6\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-4 h-4 bg-green-400 rounded-full animate-pulse pulse-glow\"></div>\n                <span className=\"text-cyan-300 font-['Exo_2'] tracking-wider\">ALL SYSTEMS OPERATIONAL</span>\n              </div>\n              <div className=\"text-cyan-400 font-['Exo_2']\">\n                <span className=\"text-xs tracking-wider uppercase\">Data Stream:</span>\n                <div className=\"hologram-text font-['Orbitron']\">LIVE</div>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <Cpu className=\"w-5 h-5 text-cyan-400 animate-pulse\" />\n                <span className=\"text-cyan-300 font-['Exo_2']\">\n                  <span className=\"text-xs tracking-wider uppercase\">Neural Nodes:</span>\n                  <div className=\"hologram-text font-['Orbitron']\">{heatmapData.length}</div>\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default UnifiedDataCenterDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAA0BC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,SAAS,EAAEC,IAAI,QAAQ,UAAU;AACtH,SAASC,aAAa,EAA0DC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAYC,GAAG,QAAyB,cAAc;;AAE9J;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,gBAAgB,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMC,YAAY,GAAGD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;EACpDD,YAAY,CAACE,WAAW,GAAGJ,gBAAgB;EAC3CC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACJ,YAAY,CAAC;AACzC;AAEA,MAAMK,0BAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,IAAI4B,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC;IACrCiC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,GAAG;IACXC,WAAW,EAAE,GAAG;IAChBC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,GAAG;IACbC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA,MAAMiD,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,GAAG,GAAG,IAAItB,IAAI,CAAC,CAAC;IACtB,MAAMuB,IAAI,GAAGD,GAAG,CAACE,QAAQ,CAAC,CAAC;IAC3B,MAAMC,MAAM,GAAGH,GAAG,CAACI,UAAU,CAAC,CAAC;IAC/B,MAAMC,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACN,IAAI,GAAG,EAAE,GAAGE,MAAM,IAAIG,IAAI,CAACE,EAAE,GAAG,GAAG,CAAC;IAEpE,OAAO;MACLzB,GAAG,EAAE,IAAI,GAAGsB,aAAa,GAAG,GAAG,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;MAC9DzB,MAAM,EAAE,IAAI,GAAGqB,aAAa,GAAG,IAAI,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;MAClExB,WAAW,EAAE,GAAG,GAAGoB,aAAa,GAAG,IAAI,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;MACtEvB,UAAU,EAAE,IAAI,GAAGmB,aAAa,GAAG,GAAG,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;MACrEtB,QAAQ,EAAE,GAAG,GAAG,CAACmB,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;MAC5CrB,QAAQ,EAAE,IAAI,GAAGiB,aAAa,GAAG,GAAG,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;MACnEpB,WAAW,EAAEiB,IAAI,CAACI,KAAK,CAAC,CAAC,GAAGJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;MAC9CnB,YAAY,EAAEgB,IAAI,CAACI,KAAK,CAAC,CAAC,GAAGJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,CAAC;IAChD,CAAC;EACH,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,UAAU,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,MAAM,CAAC;IAClD,MAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAE,aAAa,EAAE,YAAY,CAAC;IACnG,MAAMC,QAAQ,GAAG;MACfC,QAAQ,EAAE,CAAC,gCAAgC,EAAE,sBAAsB,EAAE,qBAAqB,CAAC;MAC3FC,OAAO,EAAE,CAAC,wBAAwB,EAAE,iBAAiB,EAAE,sBAAsB,CAAC;MAC9EC,IAAI,EAAE,CAAC,uBAAuB,EAAE,wBAAwB,EAAE,wBAAwB;IACpF,CAAC;IAED,MAAMC,SAAS,GAAG,CAAC,GAAGZ,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACnD,MAAMlB,MAAM,GAAG,EAAE;IAEjB,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,EAAEC,CAAC,EAAE,EAAE;MAClC,MAAMC,IAAI,GAAGR,UAAU,CAACN,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAGG,UAAU,CAACS,MAAM,CAAC,CAAC;MACtE,MAAMC,QAAQ,GAAGT,SAAS,CAACP,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAGI,SAAS,CAACQ,MAAM,CAAC,CAAC;MACxE,MAAME,WAAW,GAAGT,QAAQ,CAACM,IAAI,CAAC;MAClC,MAAMI,OAAO,GAAGD,WAAW,CAACjB,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAGc,WAAW,CAACF,MAAM,CAAC,CAAC;MAC3E,MAAMI,IAAI,GAAG,IAAI/C,IAAI,CAACA,IAAI,CAACsB,GAAG,CAAC,CAAC,GAAGM,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC,CAACiB,kBAAkB,CAAC,CAAC;MAEhFnC,MAAM,CAACoC,IAAI,CAAC;QACVC,EAAE,EAAET,CAAC,GAAG,CAAC;QACTC,IAAI;QACJI,OAAO;QACPF,QAAQ;QACRG,IAAI;QACJI,MAAM,EAAEvB,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAG;MAC3C,CAAC,CAAC;IACJ;IAEA,OAAOlB,MAAM,CAACuC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC3B,MAAMC,QAAQ,GAAG;QAAElB,QAAQ,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAE,CAAC;MACrD,OAAOgB,QAAQ,CAACD,CAAC,CAACZ,IAAI,CAAC,GAAGa,QAAQ,CAACF,CAAC,CAACX,IAAI,CAAC;IAC5C,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMc,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,IAAI,GAAG,EAAE;IACf,MAAMC,QAAQ,GAAG,EAAE;IACnB,MAAMpC,GAAG,GAAGtB,IAAI,CAACsB,GAAG,CAAC,CAAC;IAEtB,KAAK,IAAIqC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;MAChC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,EAAE,EAAEA,GAAG,EAAE,EAAE;QACjC,MAAMC,kBAAkB,GAAGjC,IAAI,CAACkC,IAAI,CAAClC,IAAI,CAACmC,GAAG,CAACH,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAGhC,IAAI,CAACmC,GAAG,CAACJ,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;QACrF,MAAMK,gBAAgB,GAAGpC,IAAI,CAACC,GAAG,CAACP,GAAG,GAAG,KAAK,GAAGqC,GAAG,GAAGC,GAAG,CAAC,GAAG,CAAC;QAC9D,MAAMK,eAAe,GAAG,CAACrC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;QACjD,MAAMmC,WAAW,GAAGR,QAAQ,GAAGG,kBAAkB,GAAG,GAAG,GAAGG,gBAAgB,GAAGC,eAAe;QAC5F,MAAME,KAAK,GAAG,EAAE,GAAGvC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,EAAE,GAAGH,IAAI,CAACC,GAAG,CAACP,GAAG,GAAG,IAAI,GAAGqC,GAAG,GAAGC,GAAG,CAAC,GAAG,EAAE;QAE7EH,IAAI,CAACR,IAAI,CAAC;UACRmB,CAAC,EAAER,GAAG;UACNS,CAAC,EAAEV,GAAG;UACNO,WAAW,EAAEtC,IAAI,CAAC0C,GAAG,CAAC,EAAE,EAAE1C,IAAI,CAAC2C,GAAG,CAAC,EAAE,EAAEL,WAAW,CAAC,CAAC;UACpDC,KAAK,EAAEvC,IAAI,CAAC0C,GAAG,CAAC,EAAE,EAAE1C,IAAI,CAAC2C,GAAG,CAAC,GAAG,EAAEJ,KAAK,CAAC,CAAC;UACzCjB,EAAE,EAAE,IAAIS,GAAG,GAAG,CAAC,IAAIC,GAAG,GAAG,CAAC,EAAE;UAC5BT,MAAM,EAAEe,WAAW,GAAG,EAAE,GAAG,UAAU,GAAGA,WAAW,GAAG,EAAE,GAAG,SAAS,GAAG;QACzE,CAAC,CAAC;MACJ;IACF;IAEA,OAAOT,IAAI;EACb,CAAC;;EAED;EACA,MAAMe,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,YAAY,GAAG,CACnB;MAAEC,IAAI,EAAE,OAAO;MAAEC,WAAW,EAAE;IAAoC,CAAC,EACnE;MAAED,IAAI,EAAE,aAAa;MAAEC,WAAW,EAAE;IAA+B,CAAC,EACpE;MAAED,IAAI,EAAE,SAAS;MAAEC,WAAW,EAAE;IAAmC,CAAC,EACpE;MAAED,IAAI,EAAE,SAAS;MAAEC,WAAW,EAAE;IAA+B,CAAC,CACjE;IAED,MAAMxC,SAAS,GAAG,CAAC,YAAY,EAAE,kBAAkB,EAAE,eAAe,EAAE,gBAAgB,CAAC;IACvF,MAAMyC,YAAY,GAAG,CAAC,GAAGhD,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACtD,MAAMd,SAAS,GAAG,EAAE;IAEpB,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,YAAY,EAAEnC,CAAC,EAAE,EAAE;MACrC,MAAMoC,OAAO,GAAGJ,YAAY,CAAC7C,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG0C,YAAY,CAAC9B,MAAM,CAAC,CAAC;MAC7E,MAAMC,QAAQ,GAAGT,SAAS,CAACP,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAGI,SAAS,CAACQ,MAAM,CAAC,CAAC;MACxE,MAAMmC,UAAU,GAAG,EAAE,GAAGlD,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;MAEtDd,SAAS,CAACgC,IAAI,CAAC;QACbC,EAAE,EAAET,CAAC,GAAG,CAAC;QACT,GAAGoC,OAAO;QACVjC,QAAQ;QACRkC,UAAU;QACVC,QAAQ,EAAED,UAAU,GAAG,EAAE,GAAG,UAAU,GAAGA,UAAU,GAAG,EAAE,GAAG,SAAS,GAAG;MACzE,CAAC,CAAC;IACJ;IAEA,OAAO7D,SAAS,CAACmC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACwB,UAAU,GAAGzB,CAAC,CAACyB,UAAU,CAAC;EAC9D,CAAC;;EAED;EACA,MAAME,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMvB,IAAI,GAAG,EAAE;IACf,MAAMnC,GAAG,GAAG,IAAItB,IAAI,CAAC,CAAC;IAEtB,KAAK,IAAIyC,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC5B,MAAMM,IAAI,GAAG,IAAI/C,IAAI,CAACsB,GAAG,CAAC2D,OAAO,CAAC,CAAC,GAAGxC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MACzD,MAAMlB,IAAI,GAAGwB,IAAI,CAACvB,QAAQ,CAAC,CAAC;MAC5B,MAAMG,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAEN,IAAI,GAAGK,IAAI,CAACE,EAAE,GAAI,EAAE,CAAC;MAErD2B,IAAI,CAACR,IAAI,CAAC;QACRF,IAAI,EAAEA,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;UAAEzB,IAAI,EAAE,SAAS;UAAEE,MAAM,EAAE;QAAU,CAAC,CAAC;QAC9E0C,KAAK,EAAE,GAAG,GAAGxC,aAAa,GAAG,GAAG,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;QAC/DmD,OAAO,EAAE,GAAG,GAAGvD,aAAa,GAAG,IAAI,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;QAClEmC,WAAW,EAAE,EAAE,GAAGvC,aAAa,GAAG,GAAG,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;QACnEoD,aAAa,EAAE,EAAE,GAAGxD,aAAa,GAAG,CAAC,GAAG,CAACC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI;MAClE,CAAC,CAAC;IACJ;IAEA,OAAO0B,IAAI;EACb,CAAC;;EAED;EACApF,SAAS,CAAC,MAAM;IACd+B,UAAU,CAACiB,eAAe,CAAC,CAAC,CAAC;IAC7BP,SAAS,CAACmB,iBAAiB,CAAC,CAAC,CAAC;IAC9BjB,cAAc,CAACwC,mBAAmB,CAAC,CAAC,CAAC;IACrCtC,YAAY,CAACsD,iBAAiB,CAAC,CAAC,CAAC;IACjCpD,iBAAiB,CAAC4D,sBAAsB,CAAC,CAAC,CAAC;EAC7C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3G,SAAS,CAAC,MAAM;IACd,MAAM+G,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BtF,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;MAC1BI,UAAU,CAACiB,eAAe,CAAC,CAAC,CAAC;MAC7BD,iBAAiB,CAAC4D,sBAAsB,CAAC,CAAC,CAAC;IAC7C,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMM,aAAa,CAACF,KAAK,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/G,SAAS,CAAC,MAAM;IACd,MAAMkH,QAAQ,GAAGF,WAAW,CAAC,MAAM;MACjCrE,cAAc,CAACwC,mBAAmB,CAAC,CAAC,CAAC;IACvC,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAM8B,aAAa,CAACC,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlH,SAAS,CAAC,MAAM;IACd,MAAMkH,QAAQ,GAAGF,WAAW,CAAC,MAAM;MACjCvE,SAAS,CAACmB,iBAAiB,CAAC,CAAC,CAAC;IAChC,CAAC,EAAE,KAAK,CAAC;IACT,OAAO,MAAMqD,aAAa,CAACC,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlH,SAAS,CAAC,MAAM;IACd,MAAMkH,QAAQ,GAAGF,WAAW,CAAC,MAAM;MACjCnE,YAAY,CAACsD,iBAAiB,CAAC,CAAC,CAAC;IACnC,CAAC,EAAE,KAAK,CAAC;IACT,OAAO,MAAMc,aAAa,CAACC,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,mBAAmB,GAAIC,IAAI,IAAK;IACpC,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,OAAO,SAAS;EAClB,CAAC;EAED,MAAMC,aAAa,GAAIhD,IAAI,IAAK;IAC9B,QAAQA,IAAI;MACV,KAAK,UAAU;QAAE,OAAO,uCAAuC;MAC/D,KAAK,SAAS;QAAE,OAAO,gDAAgD;MACvE,KAAK,MAAM;QAAE,OAAO,0CAA0C;MAC9D;QAAS,OAAO,0BAA0B;IAC5C;EACF,CAAC;EAED,MAAMiD,QAAQ,GAAGA,CAAC;IAAEC,KAAK;IAAEC,KAAK;IAAEC,IAAI;IAAExB,GAAG;IAAEyB,KAAK,GAAG;EAAO,CAAC,KAAK;IAChE,MAAMC,UAAU,GAAIH,KAAK,GAAGvB,GAAG,GAAI,GAAG;IACtC,MAAM2B,WAAW,GAAGF,KAAK,KAAK,OAAO,GAAG,SAAS,GAAGA,KAAK,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;IAC3F,MAAMG,SAAS,GAAGH,KAAK,KAAK,OAAO,GAAG,wBAAwB,GAAGA,KAAK,KAAK,KAAK,GAAG,uBAAuB,GAAG,wBAAwB;IAErI,oBACE3G,OAAA;MAAK+G,SAAS,EAAC,+DAA+D;MAAAC,QAAA,gBAC5EhH,OAAA;QAAK+G,SAAS,EAAC;MAA8C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpEpH,OAAA;QAAK+G,SAAS,EAAC,oEAAoE;QAAAC,QAAA,EAAER;MAAK;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjGpH,OAAA;QAAK+G,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDhH,OAAA;UAAK+G,SAAS,EAAC,oDAAoD;UAAAC,QAAA,GAChEP,KAAK,eAACzG,OAAA;YAAM+G,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAEN;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACNpH,OAAA;UAAK+G,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjChH,OAAA;YAAK+G,SAAS,EAAC,gCAAgC;YAACM,OAAO,EAAC,WAAW;YAAAL,QAAA,gBACjEhH,OAAA;cAAAgH,QAAA,eACEhH,OAAA;gBAAQ8D,EAAE,EAAE,QAAQ0C,KAAK,EAAG;gBAAAQ,QAAA,gBAC1BhH,OAAA;kBAAgBsH,YAAY,EAAC,GAAG;kBAACC,MAAM,EAAC;gBAAa;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACvDpH,OAAA;kBAAAgH,QAAA,gBACEhH,OAAA;oBAAawH,EAAE,EAAC;kBAAa;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC/BpH,OAAA;oBAAawH,EAAE,EAAC;kBAAe;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACPpH,OAAA;cACEyH,CAAC,EAAC,+EAA+E;cACjFC,IAAI,EAAC,MAAM;cACXC,MAAM,EAAC,wBAAwB;cAC/BC,WAAW,EAAC;YAAG;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACFpH,OAAA;cACEyH,CAAC,EAAC,+EAA+E;cACjFC,IAAI,EAAC,MAAM;cACXC,MAAM,EAAEd,WAAY;cACpBe,WAAW,EAAC,GAAG;cACfC,eAAe,EAAE,GAAGjB,UAAU,OAAQ;cACtCkB,aAAa,EAAC,OAAO;cACrBC,KAAK,EAAE;gBAAEC,MAAM,EAAE,wBAAwBlB,SAAS;cAAI;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNpH,OAAA;YAAK+G,SAAS,EAAC,mDAAmD;YAAAC,QAAA,eAChEhH,OAAA;cAAM+G,SAAS,EAAC,mDAAmD;cAAAC,QAAA,GAAExE,IAAI,CAACyF,KAAK,CAACrB,UAAU,CAAC,EAAC,GAAC;YAAA;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNpH,OAAA;QAAK+G,SAAS,EAAC,2CAA2C;QAAAC,QAAA,GAAC,OACpD,EAAC9B,GAAG,EAAEwB,IAAI;MAAA;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,oBACEpH,OAAA;IAAK+G,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBAEzChH,OAAA;MAAQ+G,SAAS,EAAC,iDAAiD;MAAAC,QAAA,gBACjEhH,OAAA;QAAK+G,SAAS,EAAC;MAAW;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjCpH,OAAA;QAAK+G,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAC9DhH,OAAA;UAAK+G,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1ChH,OAAA;YAAK+G,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBhH,OAAA,CAACH,IAAI;cAACkH,SAAS,EAAC;YAAuF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1GpH,OAAA;cAAK+G,SAAS,EAAC;YAAkF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC,eACNpH,OAAA;YAAAgH,QAAA,gBACEhH,OAAA;cAAI+G,SAAS,EAAC,oDAAoD;cAAAC,QAAA,EAAC;YAEnE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpH,OAAA;cAAK+G,SAAS,EAAC,qDAAqD;cAAAC,QAAA,EAAC;YAErE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpH,OAAA;UAAK+G,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1ChH,OAAA;YAAK+G,SAAS,EAAC,2CAA2C;YAAAC,QAAA,gBACxDhH,OAAA,CAACL,MAAM;cAACoH,SAAS,EAAC;YAA0E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/FpH,OAAA;cACEsD,IAAI,EAAC,MAAM;cACX4E,WAAW,EAAC,2BAA2B;cACvCnB,SAAS,EAAC;YAAsI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNpH,OAAA;YAAK+G,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7DhH,OAAA;cAAK+G,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxDpH,OAAA;cAAK+G,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3BtG,WAAW,CAACkD,kBAAkB,CAAC;YAAC;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpH,OAAA;YAAK+G,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ChH,OAAA;cAAK+G,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBhH,OAAA,CAACN,IAAI;gBAACqH,SAAS,EAAC;cAA4E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/FpH,OAAA;gBAAK+G,SAAS,EAAC;cAAwE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,eACNpH,OAAA;cAAK+G,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBhH,OAAA,CAACJ,IAAI;gBAACmH,SAAS,EAAC;cAA4E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/FpH,OAAA;gBAAK+G,SAAS,EAAC;cAAsF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETpH,OAAA;MAAK+G,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErChH,OAAA;QAAK+G,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDhH,OAAA;UAAK+G,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAC5EhH,OAAA;YAAK+G,SAAS,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjCpH,OAAA;YAAI+G,SAAS,EAAC,uEAAuE;YAAAC,QAAA,EAAC;UAEtF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGLpH,OAAA;YAAK+G,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACzDhH,OAAA,CAACuG,QAAQ;cAACC,KAAK,EAAC,KAAK;cAACC,KAAK,EAAE1F,OAAO,CAACE,GAAG,CAACkH,OAAO,CAAC,CAAC,CAAE;cAACzB,IAAI,EAAC,EAAE;cAACxB,GAAG,EAAE,CAAE;cAACyB,KAAK,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrFpH,OAAA,CAACuG,QAAQ;cAACC,KAAK,EAAC,SAAS;cAACC,KAAK,EAAE1F,OAAO,CAACG,MAAM,CAACiH,OAAO,CAAC,CAAC,CAAE;cAACzB,IAAI,EAAC,GAAG;cAACxB,GAAG,EAAE,CAAE;cAACyB,KAAK,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5FpH,OAAA,CAACuG,QAAQ;cAACC,KAAK,EAAC,cAAc;cAACC,KAAK,EAAEjE,IAAI,CAACyF,KAAK,CAAClH,OAAO,CAACI,WAAW,GAAG,GAAG,CAAE;cAACuF,IAAI,EAAC,KAAK;cAACxB,GAAG,EAAE,IAAK;cAACyB,KAAK,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClHpH,OAAA;cAAK+G,SAAS,EAAC,+DAA+D;cAAAC,QAAA,gBAC5EhH,OAAA;gBAAK+G,SAAS,EAAC;cAA8C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpEpH,OAAA;gBAAK+G,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChGpH,OAAA;gBAAK+G,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAAEjG,OAAO,CAACQ;cAAW;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5GpH,OAAA;gBAAK+G,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,GAAEjG,OAAO,CAACS,YAAY,EAAC,SAAO;cAAA;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzFpH,OAAA;gBAAK+G,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrChH,OAAA,CAACP,aAAa;kBAACsH,SAAS,EAAC;gBAAoC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpH,OAAA;YAAK+G,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBhH,OAAA;cAAI+G,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpH,OAAA;cAAK+G,SAAS,EAAC,0DAA0D;cAAAC,QAAA,gBACvEhH,OAAA;gBAAK+G,SAAS,EAAC;cAA8C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpEpH,OAAA,CAACV,mBAAmB;gBAAC8I,KAAK,EAAC,MAAM;gBAACC,MAAM,EAAC,MAAM;gBAAArB,QAAA,eAC7ChH,OAAA,CAACT,SAAS;kBAAC8E,IAAI,EAAEtC,cAAc,CAACuG,KAAK,CAAC,CAAC,EAAE,CAAE;kBAAAtB,QAAA,gBACzChH,OAAA;oBAAAgH,QAAA,eACEhH,OAAA;sBAAgB8D,EAAE,EAAC,oBAAoB;sBAACyE,EAAE,EAAC,GAAG;sBAACC,EAAE,EAAC,GAAG;sBAACC,EAAE,EAAC,GAAG;sBAACC,EAAE,EAAC,GAAG;sBAAA1B,QAAA,gBACjEhH,OAAA;wBAAM2I,MAAM,EAAC,IAAI;wBAACC,SAAS,EAAC,SAAS;wBAACC,WAAW,EAAE;sBAAI;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eACzDpH,OAAA;wBAAM2I,MAAM,EAAC,KAAK;wBAACC,SAAS,EAAC,SAAS;wBAACC,WAAW,EAAE;sBAAI;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eACPpH,OAAA,CAACZ,aAAa;oBAACyI,eAAe,EAAC,KAAK;oBAACF,MAAM,EAAC;kBAAwB;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvEpH,OAAA,CAACd,KAAK;oBACJ4J,OAAO,EAAC,MAAM;oBACdC,QAAQ,EAAE,KAAM;oBAChBC,QAAQ,EAAE,KAAM;oBAChBC,IAAI,EAAE;sBAAEvB,IAAI,EAAE,SAAS;sBAAEwB,QAAQ,EAAE,EAAE;sBAAEC,UAAU,EAAE;oBAAQ;kBAAE;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,eACFpH,OAAA,CAACb,KAAK;oBACJiK,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;oBACjBL,QAAQ,EAAE,KAAM;oBAChBC,QAAQ,EAAE,KAAM;oBAChBC,IAAI,EAAE;sBAAEvB,IAAI,EAAE,SAAS;sBAAEwB,QAAQ,EAAE,EAAE;sBAAEC,UAAU,EAAE;oBAAQ;kBAAE;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,eACFpH,OAAA,CAACX,OAAO;oBACNgK,YAAY,EAAE;sBACZC,eAAe,EAAE,sBAAsB;sBACvCC,MAAM,EAAE,kCAAkC;sBAC1CC,YAAY,EAAE,KAAK;sBACnB7C,KAAK,EAAE,SAAS;sBAChBwC,UAAU,EAAE;oBACd;kBAAE;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFpH,OAAA,CAACR,IAAI;oBACH8D,IAAI,EAAC,UAAU;oBACfwF,OAAO,EAAC,eAAe;oBACvBnB,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACf6B,WAAW,EAAE,CAAE;oBACf/B,IAAI,EAAC,0BAA0B;oBAC/BK,KAAK,EAAE;sBAAEC,MAAM,EAAE;oBAA+C;kBAAE;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpH,OAAA;UAAK+G,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAC5EhH,OAAA;YAAK+G,SAAS,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjCpH,OAAA;YAAI+G,SAAS,EAAC,uEAAuE;YAAAC,QAAA,EAAC;UAEtF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpH,OAAA;YAAG+G,SAAS,EAAC,0DAA0D;YAAAC,QAAA,EAAC;UAExE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGJpH,OAAA;YAAK+G,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDhH,OAAA;cAAK+G,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1ChH,OAAA;gBAAM+G,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzGpH,OAAA;gBAAK+G,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1ChH,OAAA;kBAAM+G,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClEpH,OAAA;kBAAK+G,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BhH,OAAA;oBAAK+G,SAAS,EAAC,iBAAiB;oBAACgB,KAAK,EAAE;sBAAEuB,eAAe,EAAE;oBAAU;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9EpH,OAAA;oBAAK+G,SAAS,EAAC,iBAAiB;oBAACgB,KAAK,EAAE;sBAAEuB,eAAe,EAAE;oBAAU;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9EpH,OAAA;oBAAK+G,SAAS,EAAC,iBAAiB;oBAACgB,KAAK,EAAE;sBAAEuB,eAAe,EAAE;oBAAU;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9EpH,OAAA;oBAAK+G,SAAS,EAAC,iBAAiB;oBAACgB,KAAK,EAAE;sBAAEuB,eAAe,EAAE;oBAAU;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9EpH,OAAA;oBAAK+G,SAAS,EAAC,iBAAiB;oBAACgB,KAAK,EAAE;sBAAEuB,eAAe,EAAE;oBAAU;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9EpH,OAAA;oBAAK+G,SAAS,EAAC,iBAAiB;oBAACgB,KAAK,EAAE;sBAAEuB,eAAe,EAAE;oBAAU;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC,eACNpH,OAAA;kBAAM+G,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpH,OAAA;cAAK+G,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpH,OAAA;YAAK+G,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAC1CrF,WAAW,CAAC+H,GAAG,CAAEC,IAAI,iBACpB3J,OAAA;cAEE+G,SAAS,EAAC,iGAAiG;cAC3GgB,KAAK,EAAE;gBACLuB,eAAe,EAAElD,mBAAmB,CAACuD,IAAI,CAAC7E,WAAW,CAAC;gBACtD8E,SAAS,EAAE,YAAYxD,mBAAmB,CAACuD,IAAI,CAAC7E,WAAW,CAAC;cAC9D,CAAE;cACF+E,OAAO,EAAEA,CAAA,KAAM/I,eAAe,CAAC6I,IAAI,CAAE;cAAA3C,QAAA,gBAErChH,OAAA;gBAAK+G,SAAS,EAAC;cAAiD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvEpH,OAAA;gBAAK+G,SAAS,EAAC;cAAsH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAE5IpH,OAAA;gBAAK+G,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,eAChEhH,OAAA;kBAAM+G,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC5E2C,IAAI,CAAC7F;gBAAE;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAfDuC,IAAI,CAAC7F,EAAE;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBT,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAELvG,YAAY,iBACXb,OAAA;YAAK+G,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBAClEhH,OAAA;cAAK+G,SAAS,EAAC;YAA8C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpEpH,OAAA;cAAI+G,SAAS,EAAC,gDAAgD;cAAAC,QAAA,GAAC,eAChD,EAACnG,YAAY,CAACiD,EAAE;YAAA;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACLpH,OAAA;cAAK+G,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7ChH,OAAA;gBAAAgH,QAAA,gBACEhH,OAAA;kBAAM+G,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClEpH,OAAA;kBAAK+G,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,GAC7CnG,YAAY,CAACiE,WAAW,CAACqD,OAAO,CAAC,CAAC,CAAC,EAAC,OACvC;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpH,OAAA;gBAAAgH,QAAA,gBACEhH,OAAA;kBAAM+G,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5DpH,OAAA;kBAAK+G,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,GAC7CnG,YAAY,CAACkE,KAAK,CAACoD,OAAO,CAAC,CAAC,CAAC,EAAC,GACjC;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpH,OAAA;QAAK+G,SAAS,EAAC,+DAA+D;QAAAC,QAAA,gBAC5EhH,OAAA;UAAK+G,SAAS,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjCpH,OAAA;UAAK+G,SAAS,EAAC,yDAAyD;UAAAC,QAAA,gBACtEhH,OAAA;YAAK+G,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC3ChH,OAAA;cAAM+G,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3EpH,OAAA;cAAK+G,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAEtG,WAAW,CAACkD,kBAAkB,CAAC;YAAC;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CAAC,eACNpH,OAAA;YAAK+G,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ChH,OAAA;cAAK+G,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1ChH,OAAA;gBAAK+G,SAAS,EAAC;cAA4D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClFpH,OAAA;gBAAM+G,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,eACNpH,OAAA;cAAK+G,SAAS,EAAC,8BAA8B;cAAAC,QAAA,gBAC3ChH,OAAA;gBAAM+G,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtEpH,OAAA;gBAAK+G,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNpH,OAAA;cAAK+G,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1ChH,OAAA,CAACF,GAAG;gBAACiH,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvDpH,OAAA;gBAAM+G,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC5ChH,OAAA;kBAAM+G,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvEpH,OAAA;kBAAK+G,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAAErF,WAAW,CAAC4B;gBAAM;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3G,EAAA,CAxfID,0BAA0B;AAAAsJ,EAAA,GAA1BtJ,0BAA0B;AA0fhC,eAAeA,0BAA0B;AAAC,IAAAsJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}