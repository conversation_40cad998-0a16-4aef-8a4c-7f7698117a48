{"ast": null, "code": "import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport offsetNone from \"./offset/none.js\";\nimport orderNone from \"./order/none.js\";\nfunction stackValue(d, key) {\n  return d[key];\n}\nfunction stackSeries(key) {\n  const series = [];\n  series.key = key;\n  return series;\n}\nexport default function () {\n  var keys = constant([]),\n    order = orderNone,\n    offset = offsetNone,\n    value = stackValue;\n  function stack(data) {\n    var sz = Array.from(keys.apply(this, arguments), stackSeries),\n      i,\n      n = sz.length,\n      j = -1,\n      oz;\n    for (const d of data) {\n      for (i = 0, ++j; i < n; ++i) {\n        (sz[i][j] = [0, +value(d, sz[i].key, j, data)]).data = d;\n      }\n    }\n    for (i = 0, oz = array(order(sz)); i < n; ++i) {\n      sz[oz[i]].index = i;\n    }\n    offset(sz, oz);\n    return sz;\n  }\n  stack.keys = function (_) {\n    return arguments.length ? (keys = typeof _ === \"function\" ? _ : constant(Array.from(_)), stack) : keys;\n  };\n  stack.value = function (_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(+_), stack) : value;\n  };\n  stack.order = function (_) {\n    return arguments.length ? (order = _ == null ? orderNone : typeof _ === \"function\" ? _ : constant(Array.from(_)), stack) : order;\n  };\n  stack.offset = function (_) {\n    return arguments.length ? (offset = _ == null ? offsetNone : _, stack) : offset;\n  };\n  return stack;\n}", "map": {"version": 3, "names": ["array", "constant", "offsetNone", "orderNone", "stackValue", "d", "key", "stackSeries", "series", "keys", "order", "offset", "value", "stack", "data", "sz", "Array", "from", "apply", "arguments", "i", "n", "length", "j", "oz", "index", "_"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/node_modules/d3-shape/src/stack.js"], "sourcesContent": ["import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport offsetNone from \"./offset/none.js\";\nimport orderNone from \"./order/none.js\";\n\nfunction stackValue(d, key) {\n  return d[key];\n}\n\nfunction stackSeries(key) {\n  const series = [];\n  series.key = key;\n  return series;\n}\n\nexport default function() {\n  var keys = constant([]),\n      order = orderNone,\n      offset = offsetNone,\n      value = stackValue;\n\n  function stack(data) {\n    var sz = Array.from(keys.apply(this, arguments), stackSeries),\n        i, n = sz.length, j = -1,\n        oz;\n\n    for (const d of data) {\n      for (i = 0, ++j; i < n; ++i) {\n        (sz[i][j] = [0, +value(d, sz[i].key, j, data)]).data = d;\n      }\n    }\n\n    for (i = 0, oz = array(order(sz)); i < n; ++i) {\n      sz[oz[i]].index = i;\n    }\n\n    offset(sz, oz);\n    return sz;\n  }\n\n  stack.keys = function(_) {\n    return arguments.length ? (keys = typeof _ === \"function\" ? _ : constant(Array.from(_)), stack) : keys;\n  };\n\n  stack.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(+_), stack) : value;\n  };\n\n  stack.order = function(_) {\n    return arguments.length ? (order = _ == null ? orderNone : typeof _ === \"function\" ? _ : constant(Array.from(_)), stack) : order;\n  };\n\n  stack.offset = function(_) {\n    return arguments.length ? (offset = _ == null ? offsetNone : _, stack) : offset;\n  };\n\n  return stack;\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,YAAY;AAC9B,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,SAAS,MAAM,iBAAiB;AAEvC,SAASC,UAAUA,CAACC,CAAC,EAAEC,GAAG,EAAE;EAC1B,OAAOD,CAAC,CAACC,GAAG,CAAC;AACf;AAEA,SAASC,WAAWA,CAACD,GAAG,EAAE;EACxB,MAAME,MAAM,GAAG,EAAE;EACjBA,MAAM,CAACF,GAAG,GAAGA,GAAG;EAChB,OAAOE,MAAM;AACf;AAEA,eAAe,YAAW;EACxB,IAAIC,IAAI,GAAGR,QAAQ,CAAC,EAAE,CAAC;IACnBS,KAAK,GAAGP,SAAS;IACjBQ,MAAM,GAAGT,UAAU;IACnBU,KAAK,GAAGR,UAAU;EAEtB,SAASS,KAAKA,CAACC,IAAI,EAAE;IACnB,IAAIC,EAAE,GAAGC,KAAK,CAACC,IAAI,CAACR,IAAI,CAACS,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,EAAEZ,WAAW,CAAC;MACzDa,CAAC;MAAEC,CAAC,GAAGN,EAAE,CAACO,MAAM;MAAEC,CAAC,GAAG,CAAC,CAAC;MACxBC,EAAE;IAEN,KAAK,MAAMnB,CAAC,IAAIS,IAAI,EAAE;MACpB,KAAKM,CAAC,GAAG,CAAC,EAAE,EAAEG,CAAC,EAAEH,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;QAC3B,CAACL,EAAE,CAACK,CAAC,CAAC,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAACX,KAAK,CAACP,CAAC,EAAEU,EAAE,CAACK,CAAC,CAAC,CAACd,GAAG,EAAEiB,CAAC,EAAET,IAAI,CAAC,CAAC,EAAEA,IAAI,GAAGT,CAAC;MAC1D;IACF;IAEA,KAAKe,CAAC,GAAG,CAAC,EAAEI,EAAE,GAAGxB,KAAK,CAACU,KAAK,CAACK,EAAE,CAAC,CAAC,EAAEK,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MAC7CL,EAAE,CAACS,EAAE,CAACJ,CAAC,CAAC,CAAC,CAACK,KAAK,GAAGL,CAAC;IACrB;IAEAT,MAAM,CAACI,EAAE,EAAES,EAAE,CAAC;IACd,OAAOT,EAAE;EACX;EAEAF,KAAK,CAACJ,IAAI,GAAG,UAASiB,CAAC,EAAE;IACvB,OAAOP,SAAS,CAACG,MAAM,IAAIb,IAAI,GAAG,OAAOiB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGzB,QAAQ,CAACe,KAAK,CAACC,IAAI,CAACS,CAAC,CAAC,CAAC,EAAEb,KAAK,IAAIJ,IAAI;EACxG,CAAC;EAEDI,KAAK,CAACD,KAAK,GAAG,UAASc,CAAC,EAAE;IACxB,OAAOP,SAAS,CAACG,MAAM,IAAIV,KAAK,GAAG,OAAOc,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGzB,QAAQ,CAAC,CAACyB,CAAC,CAAC,EAAEb,KAAK,IAAID,KAAK;EAC/F,CAAC;EAEDC,KAAK,CAACH,KAAK,GAAG,UAASgB,CAAC,EAAE;IACxB,OAAOP,SAAS,CAACG,MAAM,IAAIZ,KAAK,GAAGgB,CAAC,IAAI,IAAI,GAAGvB,SAAS,GAAG,OAAOuB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGzB,QAAQ,CAACe,KAAK,CAACC,IAAI,CAACS,CAAC,CAAC,CAAC,EAAEb,KAAK,IAAIH,KAAK;EAClI,CAAC;EAEDG,KAAK,CAACF,MAAM,GAAG,UAASe,CAAC,EAAE;IACzB,OAAOP,SAAS,CAACG,MAAM,IAAIX,MAAM,GAAGe,CAAC,IAAI,IAAI,GAAGxB,UAAU,GAAGwB,CAAC,EAAEb,KAAK,IAAIF,MAAM;EACjF,CAAC;EAED,OAAOE,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}