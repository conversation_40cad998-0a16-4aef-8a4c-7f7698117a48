{"ast": null, "code": "var baseFor = require('./_baseFor'),\n  keys = require('./keys');\n\n/**\n * The base implementation of `_.forOwn` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Object} Returns `object`.\n */\nfunction baseForOwn(object, iteratee) {\n  return object && baseFor(object, iteratee, keys);\n}\nmodule.exports = baseForOwn;", "map": {"version": 3, "names": ["baseFor", "require", "keys", "baseForOwn", "object", "iteratee", "module", "exports"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/node_modules/lodash/_baseForOwn.js"], "sourcesContent": ["var baseFor = require('./_baseFor'),\n    keys = require('./keys');\n\n/**\n * The base implementation of `_.forOwn` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Object} Returns `object`.\n */\nfunction baseForOwn(object, iteratee) {\n  return object && baseFor(object, iteratee, keys);\n}\n\nmodule.exports = baseForOwn;\n"], "mappings": "AAAA,IAAIA,OAAO,GAAGC,OAAO,CAAC,YAAY,CAAC;EAC/BC,IAAI,GAAGD,OAAO,CAAC,QAAQ,CAAC;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,UAAUA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EACpC,OAAOD,MAAM,IAAIJ,OAAO,CAACI,MAAM,EAAEC,QAAQ,EAAEH,IAAI,CAAC;AAClD;AAEAI,MAAM,CAACC,OAAO,GAAGJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}