{"ast": null, "code": "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n/* eslint no-console: 0 */\n\nexport var getIntersectionKeys = function getIntersectionKeys(preObj, nextObj) {\n  return [Object.keys(preObj), Object.keys(nextObj)].reduce(function (a, b) {\n    return a.filter(function (c) {\n      return b.includes(c);\n    });\n  });\n};\nexport var identity = function identity(param) {\n  return param;\n};\n\n/*\n * @description: convert camel case to dash case\n * string => string\n */\nexport var getDashCase = function getDashCase(name) {\n  return name.replace(/([A-Z])/g, function (v) {\n    return \"-\".concat(v.toLowerCase());\n  });\n};\nexport var log = function log() {\n  var _console;\n  (_console = console).log.apply(_console, arguments);\n};\n\n/*\n * @description: log the value of a varible\n * string => any => any\n */\nexport var debug = function debug(name) {\n  return function (item) {\n    log(name, item);\n    return item;\n  };\n};\n\n/*\n * @description: log name, args, return value of a function\n * function => function\n */\nexport var debugf = function debugf(tag, f) {\n  return function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var res = f.apply(void 0, args);\n    var name = tag || f.name || 'anonymous function';\n    var argNames = \"(\".concat(args.map(JSON.stringify).join(', '), \")\");\n    log(\"\".concat(name, \": \").concat(argNames, \" => \").concat(JSON.stringify(res)));\n    return res;\n  };\n};\n\n/*\n * @description: map object on every element in this object.\n * (function, object) => object\n */\nexport var mapObject = function mapObject(fn, obj) {\n  return Object.keys(obj).reduce(function (res, key) {\n    return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, key, fn(key, obj[key])));\n  }, {});\n};\nexport var getTransitionVal = function getTransitionVal(props, duration, easing) {\n  return props.map(function (prop) {\n    return \"\".concat(getDashCase(prop), \" \").concat(duration, \"ms \").concat(easing);\n  }).join(',');\n};\nvar isDev = process.env.NODE_ENV !== 'production';\nexport var warn = function warn(condition, format, a, b, c, d, e, f) {\n  if (isDev && typeof console !== 'undefined' && console.warn) {\n    if (format === undefined) {\n      console.warn('LogUtils requires an error message argument');\n    }\n    if (!condition) {\n      if (format === undefined) {\n        console.warn('Minified exception occurred; use the non-minified dev environment ' + 'for the full error message and additional helpful warnings.');\n      } else {\n        var args = [a, b, c, d, e, f];\n        var argIndex = 0;\n        console.warn(format.replace(/%s/g, function () {\n          return args[argIndex++];\n        }));\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "arg", "_toPrimitive", "String", "input", "hint", "prim", "toPrimitive", "undefined", "res", "call", "TypeError", "Number", "getIntersectionKeys", "preObj", "nextObj", "reduce", "a", "b", "c", "includes", "identity", "param", "getDashCase", "name", "replace", "v", "concat", "toLowerCase", "log", "_console", "console", "debug", "item", "debugf", "tag", "f", "_len", "args", "Array", "_key", "argNames", "map", "JSON", "stringify", "join", "mapObject", "fn", "getTransitionVal", "props", "duration", "easing", "prop", "isDev", "process", "env", "NODE_ENV", "warn", "condition", "format", "d", "argIndex"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/node_modules/react-smooth/es6/util.js"], "sourcesContent": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/* eslint no-console: 0 */\n\nexport var getIntersectionKeys = function getIntersectionKeys(preObj, nextObj) {\n  return [Object.keys(preObj), Object.keys(nextObj)].reduce(function (a, b) {\n    return a.filter(function (c) {\n      return b.includes(c);\n    });\n  });\n};\nexport var identity = function identity(param) {\n  return param;\n};\n\n/*\n * @description: convert camel case to dash case\n * string => string\n */\nexport var getDashCase = function getDashCase(name) {\n  return name.replace(/([A-Z])/g, function (v) {\n    return \"-\".concat(v.toLowerCase());\n  });\n};\nexport var log = function log() {\n  var _console;\n  (_console = console).log.apply(_console, arguments);\n};\n\n/*\n * @description: log the value of a varible\n * string => any => any\n */\nexport var debug = function debug(name) {\n  return function (item) {\n    log(name, item);\n    return item;\n  };\n};\n\n/*\n * @description: log name, args, return value of a function\n * function => function\n */\nexport var debugf = function debugf(tag, f) {\n  return function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var res = f.apply(void 0, args);\n    var name = tag || f.name || 'anonymous function';\n    var argNames = \"(\".concat(args.map(JSON.stringify).join(', '), \")\");\n    log(\"\".concat(name, \": \").concat(argNames, \" => \").concat(JSON.stringify(res)));\n    return res;\n  };\n};\n\n/*\n * @description: map object on every element in this object.\n * (function, object) => object\n */\nexport var mapObject = function mapObject(fn, obj) {\n  return Object.keys(obj).reduce(function (res, key) {\n    return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, key, fn(key, obj[key])));\n  }, {});\n};\nexport var getTransitionVal = function getTransitionVal(props, duration, easing) {\n  return props.map(function (prop) {\n    return \"\".concat(getDashCase(prop), \" \").concat(duration, \"ms \").concat(easing);\n  }).join(',');\n};\nvar isDev = process.env.NODE_ENV !== 'production';\nexport var warn = function warn(condition, format, a, b, c, d, e, f) {\n  if (isDev && typeof console !== 'undefined' && console.warn) {\n    if (format === undefined) {\n      console.warn('LogUtils requires an error message argument');\n    }\n    if (!condition) {\n      if (format === undefined) {\n        console.warn('Minified exception occurred; use the non-minified dev environment ' + 'for the full error message and additional helpful warnings.');\n      } else {\n        var args = [a, b, c, d, e, f];\n        var argIndex = 0;\n        console.warn(format.replace(/%s/g, function () {\n          return args[argIndex++];\n        }));\n      }\n    }\n  }\n};"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIX,CAAC,GAAGS,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKP,CAAC,GAAGA,CAAC,CAACY,MAAM,CAAC,UAAUL,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACI,wBAAwB,CAACP,CAAC,EAAEC,CAAC,CAAC,CAACO,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,CAAC,CAACO,IAAI,CAACC,KAAK,CAACR,CAAC,EAAER,CAAC,CAAC;EAAE;EAAE,OAAOQ,CAAC;AAAE;AAC9P,SAASS,aAAaA,CAACX,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,SAAS,CAACC,MAAM,EAAEZ,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIU,SAAS,CAACX,CAAC,CAAC,GAAGW,SAAS,CAACX,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEc,eAAe,CAACf,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACa,yBAAyB,GAAGb,MAAM,CAACc,gBAAgB,CAACjB,CAAC,EAAEG,MAAM,CAACa,yBAAyB,CAACd,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEE,MAAM,CAACe,cAAc,CAAClB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACI,wBAAwB,CAACL,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASe,eAAeA,CAACI,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAAED,GAAG,GAAGE,cAAc,CAACF,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAID,GAAG,EAAE;IAAEhB,MAAM,CAACe,cAAc,CAACC,GAAG,EAAEC,GAAG,EAAE;MAAEC,KAAK,EAAEA,KAAK;MAAEb,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEL,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;EAAE;EAAE,OAAOF,GAAG;AAAE;AAC3O,SAASG,cAAcA,CAACG,GAAG,EAAE;EAAE,IAAIL,GAAG,GAAGM,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAOhC,OAAO,CAAC2B,GAAG,CAAC,KAAK,QAAQ,GAAGA,GAAG,GAAGO,MAAM,CAACP,GAAG,CAAC;AAAE;AAC5H,SAASM,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAIpC,OAAO,CAACmC,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACjC,MAAM,CAACoC,WAAW,CAAC;EAAE,IAAID,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGH,IAAI,CAACI,IAAI,CAACN,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAIpC,OAAO,CAACwC,GAAG,CAAC,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIE,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAACN,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGS,MAAM,EAAER,KAAK,CAAC;AAAE;AAC5X;;AAEA,OAAO,IAAIS,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC7E,OAAO,CAACpC,MAAM,CAACC,IAAI,CAACkC,MAAM,CAAC,EAAEnC,MAAM,CAACC,IAAI,CAACmC,OAAO,CAAC,CAAC,CAACC,MAAM,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACxE,OAAOD,CAAC,CAACnC,MAAM,CAAC,UAAUqC,CAAC,EAAE;MAC3B,OAAOD,CAAC,CAACE,QAAQ,CAACD,CAAC,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAIE,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;EAC7C,OAAOA,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,IAAI,EAAE;EAClD,OAAOA,IAAI,CAACC,OAAO,CAAC,UAAU,EAAE,UAAUC,CAAC,EAAE;IAC3C,OAAO,GAAG,CAACC,MAAM,CAACD,CAAC,CAACE,WAAW,CAAC,CAAC,CAAC;EACpC,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAIC,GAAG,GAAG,SAASA,GAAGA,CAAA,EAAG;EAC9B,IAAIC,QAAQ;EACZ,CAACA,QAAQ,GAAGC,OAAO,EAAEF,GAAG,CAAC3C,KAAK,CAAC4C,QAAQ,EAAE1C,SAAS,CAAC;AACrD,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,IAAI4C,KAAK,GAAG,SAASA,KAAKA,CAACR,IAAI,EAAE;EACtC,OAAO,UAAUS,IAAI,EAAE;IACrBJ,GAAG,CAACL,IAAI,EAAES,IAAI,CAAC;IACf,OAAOA,IAAI;EACb,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,GAAG,EAAEC,CAAC,EAAE;EAC1C,OAAO,YAAY;IACjB,KAAK,IAAIC,IAAI,GAAGjD,SAAS,CAACC,MAAM,EAAEiD,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGpD,SAAS,CAACoD,IAAI,CAAC;IAC9B;IACA,IAAI/B,GAAG,GAAG2B,CAAC,CAAClD,KAAK,CAAC,KAAK,CAAC,EAAEoD,IAAI,CAAC;IAC/B,IAAId,IAAI,GAAGW,GAAG,IAAIC,CAAC,CAACZ,IAAI,IAAI,oBAAoB;IAChD,IAAIiB,QAAQ,GAAG,GAAG,CAACd,MAAM,CAACW,IAAI,CAACI,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;IACnEhB,GAAG,CAAC,EAAE,CAACF,MAAM,CAACH,IAAI,EAAE,IAAI,CAAC,CAACG,MAAM,CAACc,QAAQ,EAAE,MAAM,CAAC,CAACd,MAAM,CAACgB,IAAI,CAACC,SAAS,CAACnC,GAAG,CAAC,CAAC,CAAC;IAC/E,OAAOA,GAAG;EACZ,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,IAAIqC,SAAS,GAAG,SAASA,SAASA,CAACC,EAAE,EAAEpD,GAAG,EAAE;EACjD,OAAOhB,MAAM,CAACC,IAAI,CAACe,GAAG,CAAC,CAACqB,MAAM,CAAC,UAAUP,GAAG,EAAEb,GAAG,EAAE;IACjD,OAAOT,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsB,GAAG,CAAC,EAAE,CAAC,CAAC,EAAElB,eAAe,CAAC,CAAC,CAAC,EAAEK,GAAG,EAAEmD,EAAE,CAACnD,GAAG,EAAED,GAAG,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC/F,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC;AACD,OAAO,IAAIoD,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EAC/E,OAAOF,KAAK,CAACP,GAAG,CAAC,UAAUU,IAAI,EAAE;IAC/B,OAAO,EAAE,CAACzB,MAAM,CAACJ,WAAW,CAAC6B,IAAI,CAAC,EAAE,GAAG,CAAC,CAACzB,MAAM,CAACuB,QAAQ,EAAE,KAAK,CAAC,CAACvB,MAAM,CAACwB,MAAM,CAAC;EACjF,CAAC,CAAC,CAACN,IAAI,CAAC,GAAG,CAAC;AACd,CAAC;AACD,IAAIQ,KAAK,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AACjD,OAAO,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,SAAS,EAAEC,MAAM,EAAE1C,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEyC,CAAC,EAAEpF,CAAC,EAAE4D,CAAC,EAAE;EACnE,IAAIiB,KAAK,IAAI,OAAOtB,OAAO,KAAK,WAAW,IAAIA,OAAO,CAAC0B,IAAI,EAAE;IAC3D,IAAIE,MAAM,KAAKnD,SAAS,EAAE;MACxBuB,OAAO,CAAC0B,IAAI,CAAC,6CAA6C,CAAC;IAC7D;IACA,IAAI,CAACC,SAAS,EAAE;MACd,IAAIC,MAAM,KAAKnD,SAAS,EAAE;QACxBuB,OAAO,CAAC0B,IAAI,CAAC,oEAAoE,GAAG,6DAA6D,CAAC;MACpJ,CAAC,MAAM;QACL,IAAInB,IAAI,GAAG,CAACrB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEyC,CAAC,EAAEpF,CAAC,EAAE4D,CAAC,CAAC;QAC7B,IAAIyB,QAAQ,GAAG,CAAC;QAChB9B,OAAO,CAAC0B,IAAI,CAACE,MAAM,CAAClC,OAAO,CAAC,KAAK,EAAE,YAAY;UAC7C,OAAOa,IAAI,CAACuB,QAAQ,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;MACL;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}