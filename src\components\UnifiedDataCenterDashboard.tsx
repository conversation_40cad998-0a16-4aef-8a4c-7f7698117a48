import React, { useState, useEffect } from 'react';
import { <PERSON>Chart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import { AlertTriangle, Thermometer, Zap, Server, Shield, TrendingUp, Settings, Bell, Search, User, Menu, Activity, Cpu, HardDrive, Wifi } from 'lucide-react';

// Futuristic CSS styles
const futuristicStyles = `
  @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap');
  
  .futuristic-bg {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0a0a0a 100%);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
  }
  
  .futuristic-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(0, 255, 0, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }
  
  .neon-border {
    border: 1px solid rgba(0, 255, 255, 0.3);
    box-shadow: 
      0 0 10px rgba(0, 255, 255, 0.2),
      inset 0 0 10px rgba(0, 255, 255, 0.1);
    background: rgba(0, 20, 40, 0.8);
    backdrop-filter: blur(10px);
  }
  
  .neon-glow {
    box-shadow: 
      0 0 20px rgba(0, 255, 255, 0.4),
      0 0 40px rgba(0, 255, 255, 0.2),
      inset 0 0 20px rgba(0, 255, 255, 0.1);
  }
  
  .hologram-text {
    font-family: 'Orbitron', monospace;
    color: #00ffff;
    text-shadow: 
      0 0 10px rgba(0, 255, 255, 0.8),
      0 0 20px rgba(0, 255, 255, 0.4),
      0 0 30px rgba(0, 255, 255, 0.2);
  }
  
  .data-stream {
    background: linear-gradient(90deg, transparent 0%, rgba(0, 255, 255, 0.1) 50%, transparent 100%);
    animation: dataFlow 3s linear infinite;
  }
  
  @keyframes dataFlow {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }
  
  .pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite alternate;
  }
  
  @keyframes pulseGlow {
    from { box-shadow: 0 0 20px rgba(0, 255, 255, 0.4); }
    to { box-shadow: 0 0 30px rgba(0, 255, 255, 0.8), 0 0 40px rgba(0, 255, 255, 0.4); }
  }
  
  .matrix-bg {
    background: 
      linear-gradient(90deg, transparent 98%, rgba(0, 255, 255, 0.1) 100%),
      linear-gradient(0deg, transparent 98%, rgba(0, 255, 255, 0.1) 100%);
    background-size: 20px 20px;
  }
  
  .scan-line {
    position: relative;
    overflow: hidden;
  }
  
  .scan-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    animation: scanLine 3s linear infinite;
  }
  
  @keyframes scanLine {
    0% { left: -100%; }
    100% { left: 100%; }
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(0, 20, 40, 0.5);
    border-radius: 4px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #00ffff, #0080ff);
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.4);
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #00ffff, #00aaff);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.6);
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .hologram-text {
      font-size: 0.9em;
    }
    
    .neon-border {
      padding: 1rem;
    }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = futuristicStyles;
  document.head.appendChild(styleElement);
}

const UnifiedDataCenterDashboard = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [selectedRack, setSelectedRack] = useState(null);
  const [kpiData, setKpiData] = useState({
    pue: 1.45,
    itLoad: 1.1,
    coolingLoad: 1.1,
    trialPower: 2.4,
    upsPower: 1.11,
    pduPower: 1.5,
    totalAlarms: 4,
    activeAlarms: 2
  });
  const [alarms, setAlarms] = useState([]);
  const [heatmapData, setHeatmapData] = useState([]);
  const [anomalies, setAnomalies] = useState([]);
  const [timeSeriesData, setTimeSeriesData] = useState([]);

  // Generate realistic KPI data
  const generateKPIData = () => {
    const now = new Date();
    const hour = now.getHours();
    const minute = now.getMinutes();
    const timeVariation = Math.sin((hour * 60 + minute) * Math.PI / 720);

    return {
      pue: 1.42 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.02,
      itLoad: 1.08 + timeVariation * 0.15 + (Math.random() - 0.5) * 0.03,
      coolingLoad: 1.1 + timeVariation * 0.08 + (Math.random() - 0.5) * 0.02,
      trialPower: 2.35 + timeVariation * 0.2 + (Math.random() - 0.5) * 0.05,
      upsPower: 1.1 + (Math.random() - 0.5) * 0.01,
      pduPower: 1.48 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.02,
      totalAlarms: Math.floor(3 + Math.random() * 5),
      activeAlarms: Math.floor(1 + Math.random() * 3)
    };
  };

  // Generate alarm data
  const generateAlarmData = () => {
    const alarmTypes = ['Critical', 'Warning', 'Info'];
    const locations = ['Rack A1', 'Rack B3', 'UPS Room', 'Cooling Unit 2', 'Switch Core', 'PDU Zone C'];
    const messages = {
      Critical: ['Temperature Threshold Exceeded', 'Power Supply Failure', 'Cooling System Down'],
      Warning: ['High Humidity Detected', 'UPS Battery Low', 'Network Latency High'],
      Info: ['Scheduled Maintenance', 'System Update Complete', 'Backup Process Started']
    };

    const numAlarms = 3 + Math.floor(Math.random() * 4);
    const alarms = [];

    for (let i = 0; i < numAlarms; i++) {
      const type = alarmTypes[Math.floor(Math.random() * alarmTypes.length)];
      const location = locations[Math.floor(Math.random() * locations.length)];
      const messageList = messages[type];
      const message = messageList[Math.floor(Math.random() * messageList.length)];
      const time = new Date(Date.now() - Math.random() * 3600000).toLocaleTimeString();

      alarms.push({
        id: i + 1,
        type,
        message,
        location,
        time,
        status: Math.random() > 0.5 ? 'Active' : 'Acknowledged'
      });
    }

    return alarms.sort((a, b) => {
      const priority = { Critical: 3, Warning: 2, Info: 1 };
      return priority[b.type] - priority[a.type];
    });
  };

  // Generate heatmap data
  const generateHeatmapData = () => {
    const data = [];
    const baseTemp = 22;
    const now = Date.now();

    for (let row = 0; row < 8; row++) {
      for (let col = 0; col < 10; col++) {
        const distanceFromCenter = Math.sqrt(Math.pow(col - 4.5, 2) + Math.pow(row - 3.5, 2));
        const hotSpotInfluence = Math.sin(now / 10000 + row * col) * 2;
        const randomVariation = (Math.random() - 0.5) * 3;
        const temperature = baseTemp + distanceFromCenter * 0.8 + hotSpotInfluence + randomVariation;
        const power = 60 + Math.random() * 30 + Math.sin(now / 8000 + row + col) * 10;

        data.push({
          x: col,
          y: row,
          temperature: Math.max(18, Math.min(32, temperature)),
          power: Math.max(20, Math.min(100, power)),
          id: `R${row + 1}C${col + 1}`,
          status: temperature > 28 ? 'critical' : temperature > 25 ? 'warning' : 'normal'
        });
      }
    }

    return data;
  };

  // Generate anomaly data
  const generateAnomalies = () => {
    const anomalyTypes = [
      { icon: 'power', description: 'Unusual power consumption pattern' },
      { icon: 'temperature', description: 'Temperature gradient anomaly' },
      { icon: 'network', description: 'Network bandwidth surge detected' },
      { icon: 'cooling', description: 'HVAC performance degradation' }
    ];

    const locations = ['Rack A3-A5', 'Zone B, Rows 3-4', 'Core switches', 'Cooling Unit 1'];
    const numAnomalies = 2 + Math.floor(Math.random() * 3);
    const anomalies = [];

    for (let i = 0; i < numAnomalies; i++) {
      const anomaly = anomalyTypes[Math.floor(Math.random() * anomalyTypes.length)];
      const location = locations[Math.floor(Math.random() * locations.length)];
      const confidence = 75 + Math.floor(Math.random() * 25);

      anomalies.push({
        id: i + 1,
        ...anomaly,
        location,
        confidence,
        severity: confidence > 90 ? 'critical' : confidence > 80 ? 'warning' : 'info'
      });
    }

    return anomalies.sort((a, b) => b.confidence - a.confidence);
  };

  // Generate time series data
  const generateTimeSeriesData = () => {
    const data = [];
    const now = new Date();

    for (let i = 23; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 60 * 60 * 1000);
      const hour = time.getHours();
      const timeVariation = Math.sin((hour * Math.PI) / 12);

      data.push({
        time: time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
        power: 1.4 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.05,
        cooling: 1.1 + timeVariation * 0.08 + (Math.random() - 0.5) * 0.03,
        temperature: 23 + timeVariation * 1.5 + (Math.random() - 0.5) * 0.8,
        effectiveness: 85 + timeVariation * 5 + (Math.random() - 0.5) * 3
      });
    }

    return data;
  };

  // Initialize data on component mount
  useEffect(() => {
    setKpiData(generateKPIData());
    setAlarms(generateAlarmData());
    setHeatmapData(generateHeatmapData());
    setAnomalies(generateAnomalies());
    setTimeSeriesData(generateTimeSeriesData());
  }, []);

  // Update time and KPI data every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
      setKpiData(generateKPIData());
      setTimeSeriesData(generateTimeSeriesData());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Update heatmap every 3 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setHeatmapData(generateHeatmapData());
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  // Update alarms every 15 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setAlarms(generateAlarmData());
    }, 15000);
    return () => clearInterval(interval);
  }, []);

  // Update anomalies every 20 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setAnomalies(generateAnomalies());
    }, 20000);
    return () => clearInterval(interval);
  }, []);

  const getTemperatureColor = (temp) => {
    if (temp < 20) return '#4ade80';
    if (temp < 22) return '#84cc16';
    if (temp < 24) return '#eab308';
    if (temp < 26) return '#f97316';
    if (temp < 28) return '#ef4444';
    return '#dc2626';
  };

  const getAlarmColor = (type) => {
    switch (type) {
      case 'Critical': return 'bg-red-50 text-red-700 border-red-200';
      case 'Warning': return 'bg-yellow-50 text-yellow-700 border-yellow-200';
      case 'Info': return 'bg-blue-50 text-blue-700 border-blue-200';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const KPIGauge = ({ title, value, unit, max, color = 'blue' }) => {
    const percentage = (value / max) * 100;
    const strokeColor = color === 'green' ? '#00ff88' : color === 'red' ? '#ff0044' : '#00ffff';
    const glowColor = color === 'green' ? 'rgba(0, 255, 136, 0.4)' : color === 'red' ? 'rgba(255, 0, 68, 0.4)' : 'rgba(0, 255, 255, 0.4)';

    return (
      <div className="neon-border p-6 rounded-lg relative overflow-hidden matrix-bg">
        <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
        <div className="text-sm text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase">{title}</div>
        <div className="flex items-center justify-between">
          <div className="text-3xl font-bold hologram-text font-['Orbitron']">
            {value}<span className="text-lg text-cyan-300">{unit}</span>
          </div>
          <div className="w-20 h-20 relative">
            <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
              <defs>
                <filter id={`glow-${title}`}>
                  <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                  <feMerge>
                    <feMergeNode in="coloredBlur"/>
                    <feMergeNode in="SourceGraphic"/>
                  </feMerge>
                </filter>
              </defs>
              <path
                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke="rgba(0, 255, 255, 0.2)"
                strokeWidth="2"
              />
              <path
                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke={strokeColor}
                strokeWidth="3"
                strokeDasharray={`${percentage}, 100`}
                strokeLinecap="round"
                style={{ filter: `drop-shadow(0 0 10px ${glowColor})` }}
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-sm font-bold hologram-text font-['Orbitron']">{Math.round(percentage)}%</span>
            </div>
          </div>
        </div>
        <div className="mt-2 text-xs text-cyan-500 font-['Exo_2']">
          MAX: {max}{unit}
        </div>
      </div>
    );
  };

  return (
    <div className="futuristic-bg min-h-screen">
      {/* Header */}
      <header className="neon-border backdrop-blur-md px-6 py-4 relative">
        <div className="scan-line"></div>
        <div className="flex items-center justify-between relative z-10">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Menu className="w-8 h-8 text-cyan-400 pulse-glow cursor-pointer hover:text-cyan-300 transition-colors" />
              <div className="absolute inset-0 w-8 h-8 border border-cyan-400 rounded opacity-30 animate-pulse"></div>
            </div>
            <div>
              <h1 className="text-2xl font-bold hologram-text font-['Orbitron']">
                UNIFIED DATA CENTER
              </h1>
              <div className="text-sm text-cyan-300 font-['Exo_2'] tracking-wider">
                NEURAL COMMAND INTERFACE
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-6">
            <div className="relative neon-border rounded-lg px-4 py-2">
              <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-cyan-400" />
              <input
                type="text"
                placeholder="Search neural networks..."
                className="pl-10 pr-4 py-1 bg-transparent text-cyan-300 placeholder-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-400 font-['Exo_2']"
              />
            </div>
            <div className="text-sm text-cyan-300 font-['Exo_2'] font-mono">
              <div className="text-xs text-cyan-500">SYSTEM TIME</div>
              <div className="hologram-text">
                {currentTime.toLocaleTimeString()}
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Bell className="w-6 h-6 text-cyan-400 hover:text-cyan-300 transition-colors cursor-pointer" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              </div>
              <div className="relative">
                <User className="w-6 h-6 text-cyan-400 hover:text-cyan-300 transition-colors cursor-pointer" />
                <div className="absolute inset-0 w-6 h-6 border border-cyan-400 rounded-full animate-ping opacity-20"></div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="p-6 space-y-6 relative">
        {/* Top Row - Executive Overview & Energy Console */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Executive Overview */}
          <div className="neon-border rounded-lg p-6 relative overflow-hidden matrix-bg">
            <div className="scan-line"></div>
            <h2 className="text-xl font-bold mb-6 hologram-text font-['Orbitron'] tracking-wider">
              EXECUTIVE OVERVIEW
            </h2>

            {/* KPI Cards */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <KPIGauge title="PUE" value={kpiData.pue.toFixed(2)} unit="" max={3} color="green" />
              <KPIGauge title="IT Load" value={kpiData.itLoad.toFixed(1)} unit="M" max={2} color="blue" />
              <KPIGauge title="Cooling Load" value={Math.round(kpiData.coolingLoad * 650)} unit=" RT" max={1000} color="blue" />
              <div className="neon-border p-4 rounded-lg relative overflow-hidden matrix-bg">
                <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                <div className="text-sm text-cyan-400 mb-2 font-['Exo_2'] tracking-wider uppercase">Alarms</div>
                <div className="text-2xl font-bold text-red-400 hologram-text font-['Orbitron']">{kpiData.totalAlarms}</div>
                <div className="text-xs text-cyan-500 font-['Exo_2']">{kpiData.activeAlarms} Active</div>
                <div className="absolute top-2 right-2">
                  <AlertTriangle className="w-5 h-5 text-red-400 animate-pulse" />
                </div>
              </div>
            </div>

            {/* Thermal Effectiveness Chart */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase">
                Thermal Effectiveness Matrix
              </h3>
              <div className="h-40 neon-border rounded-lg p-4 relative overflow-hidden">
                <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={timeSeriesData.slice(-12)}>
                    <defs>
                      <linearGradient id="colorEffectiveness" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#00ffff" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#00ffff" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="2 2" stroke="rgba(0, 255, 255, 0.2)" />
                    <XAxis
                      dataKey="time"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}
                    />
                    <YAxis
                      domain={[75, 95]}
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(0, 20, 40, 0.9)',
                        border: '1px solid rgba(0, 255, 255, 0.3)',
                        borderRadius: '8px',
                        color: '#00ffff',
                        fontFamily: 'Exo 2'
                      }}
                    />
                    <Area
                      type="monotone"
                      dataKey="effectiveness"
                      stroke="#00ffff"
                      strokeWidth={2}
                      fillOpacity={1}
                      fill="url(#colorEffectiveness)"
                      style={{ filter: 'drop-shadow(0 0 10px rgba(0, 255, 255, 0.4))' }}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>

          {/* Asset 360 - Thermal Heatmap */}
          <div className="neon-border rounded-lg p-6 relative overflow-hidden matrix-bg">
            <div className="scan-line"></div>
            <h2 className="text-xl font-bold mb-2 hologram-text font-['Orbitron'] tracking-wider">
              ASSET 360 - THERMAL HEATMAP
            </h2>
            <p className="text-sm text-cyan-400 mb-6 font-['Exo_2'] tracking-wider">
              Data Hall Temperature Distribution
            </p>

            {/* Color Legend */}
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-xs text-cyan-400 font-['Exo_2'] tracking-wider uppercase">Temperature Scale:</span>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-cyan-300 font-['Exo_2']">COOL</span>
                  <div className="flex space-x-1">
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#4ade80' }}></div>
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#84cc16' }}></div>
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#eab308' }}></div>
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#f97316' }}></div>
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#ef4444' }}></div>
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#dc2626' }}></div>
                  </div>
                  <span className="text-xs text-cyan-300 font-['Exo_2']">HOT</span>
                </div>
              </div>
              <div className="text-xs text-cyan-500 font-['Exo_2']">
                Click rack for details
              </div>
            </div>

            <div className="grid grid-cols-10 gap-1 mb-4">
              {heatmapData.map((rack) => (
                <div
                  key={rack.id}
                  className="aspect-square rounded cursor-pointer transition-all duration-300 hover:scale-110 relative group"
                  style={{
                    backgroundColor: getTemperatureColor(rack.temperature),
                    boxShadow: `0 0 10px ${getTemperatureColor(rack.temperature)}40`
                  }}
                  onClick={() => setSelectedRack(rack)}
                >
                  <div className="absolute inset-0 bg-black bg-opacity-20 rounded"></div>
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-cyan-400 bg-opacity-20 rounded"></div>
                  {/* Rack Number and Temperature Display */}
                  <div className="absolute inset-0 flex flex-col items-center justify-center p-1">
                    <span className="text-xs font-bold text-white drop-shadow-lg font-['Orbitron'] leading-tight">
                      {rack.id}
                    </span>
                    <span className="text-xs font-semibold text-white drop-shadow-lg font-['Exo_2'] leading-tight">
                      {rack.temperature.toFixed(1)}°C
                    </span>
                  </div>
                </div>
              ))}
            </div>

            {selectedRack && (
              <div className="neon-border p-4 rounded-lg relative overflow-hidden">
                <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                <h4 className="font-bold text-cyan-400 mb-2 font-['Orbitron']">
                  Neural Node: {selectedRack.id}
                </h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-cyan-500 font-['Exo_2']">Temperature:</span>
                    <div className="hologram-text font-['Orbitron']">
                      {selectedRack.temperature.toFixed(1)}°C
                    </div>
                  </div>
                  <div>
                    <span className="text-cyan-500 font-['Exo_2']">Power:</span>
                    <div className="hologram-text font-['Orbitron']">
                      {selectedRack.power.toFixed(0)}%
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Recent Alarms Section */}
        <div className="neon-border rounded-lg p-6 relative overflow-hidden matrix-bg">
          <div className="scan-line"></div>
          <h2 className="text-xl font-bold mb-6 hologram-text font-['Orbitron'] tracking-wider">
            RECENT ALARMS
          </h2>

          <div className="space-y-3">
            {alarms.slice(0, 5).map((alarm) => {
              const getAlarmIcon = () => {
                switch (alarm.type) {
                  case 'Critical':
                    return <AlertTriangle className="w-5 h-5 text-red-400 animate-pulse" />;
                  case 'Warning':
                    return <AlertTriangle className="w-5 h-5 text-yellow-400" />;
                  default:
                    return <AlertTriangle className="w-5 h-5 text-blue-400" />;
                }
              };

              const getAlarmBorderColor = () => {
                switch (alarm.type) {
                  case 'Critical':
                    return 'border-red-400';
                  case 'Warning':
                    return 'border-yellow-400';
                  default:
                    return 'border-blue-400';
                }
              };

              const getAlarmTextColor = () => {
                switch (alarm.type) {
                  case 'Critical':
                    return 'text-red-400';
                  case 'Warning':
                    return 'text-yellow-400';
                  default:
                    return 'text-blue-400';
                }
              };

              return (
                <div
                  key={alarm.id}
                  className={`neon-border ${getAlarmBorderColor()} p-4 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300`}
                >
                  <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      {getAlarmIcon()}
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-1">
                          <span className={`text-sm font-bold ${getAlarmTextColor()} font-['Orbitron'] tracking-wider`}>
                            {alarm.type.toUpperCase()}
                          </span>
                          <span className="text-xs text-cyan-500 font-['Exo_2']">
                            {alarm.location}
                          </span>
                        </div>
                        <div className="text-sm text-cyan-300 font-['Exo_2']">
                          {alarm.message}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-cyan-400 font-['Exo_2'] mb-1">
                        {alarm.time}
                      </div>
                      <div className={`text-xs font-bold ${alarm.status === 'Active' ? 'text-red-400' : 'text-green-400'} font-['Orbitron']`}>
                        {alarm.status.toUpperCase()}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* View All Alarms Button */}
          <div className="mt-6 text-center">
            <button className="neon-border px-6 py-2 rounded-lg text-cyan-400 hover:text-cyan-300 hover:neon-glow transition-all duration-300 font-['Exo_2'] tracking-wider uppercase text-sm">
              View All Alarms ({alarms.length})
            </button>
          </div>
        </div>

        {/* Predictive AI Insights */}
        <div className="neon-border rounded-lg p-6 relative overflow-hidden matrix-bg">
          <div className="scan-line"></div>
          <h2 className="text-xl font-bold mb-6 hologram-text font-['Orbitron'] tracking-wider">
            PREDICTIVE AI INSIGHTS
          </h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column - Cooling Forecast & Recommendations */}
            <div className="space-y-6">
              {/* Cooling Forecast */}
              <div>
                <h3 className="text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase">
                  Cooling Forecast
                </h3>
                <div className="h-40 neon-border rounded-lg p-4 relative overflow-hidden">
                  <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={timeSeriesData.slice(-12)}>
                      <CartesianGrid strokeDasharray="2 2" stroke="rgba(0, 255, 255, 0.2)" />
                      <XAxis
                        dataKey="time"
                        axisLine={false}
                        tickLine={false}
                        tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}
                      />
                      <YAxis
                        domain={[20, 26]}
                        axisLine={false}
                        tickLine={false}
                        tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'rgba(0, 20, 40, 0.9)',
                          border: '1px solid rgba(0, 255, 255, 0.3)',
                          borderRadius: '8px',
                          color: '#00ffff',
                          fontFamily: 'Exo 2'
                        }}
                      />
                      <Line
                        type="monotone"
                        dataKey="temperature"
                        stroke="#f59e0b"
                        strokeWidth={3}
                        dot={false}
                        style={{ filter: 'drop-shadow(0 0 10px rgba(245, 158, 11, 0.6))' }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Recommendations */}
              <div>
                <h3 className="text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase">
                  Recommendations
                </h3>
                <div className="space-y-3">
                  <div className="neon-border p-3 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300">
                    <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                    <div className="flex justify-between items-center">
                      <span className="text-cyan-300 font-['Exo_2'] text-sm">Optimize I/O airflows Sector A</span>
                      <span className="text-green-400 font-['Orbitron'] font-bold text-xs">EXEC A</span>
                    </div>
                  </div>
                  <div className="neon-border p-3 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300">
                    <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                    <div className="flex justify-between items-center">
                      <span className="text-cyan-300 font-['Exo_2'] text-sm">Monitor return chilled water</span>
                      <span className="text-yellow-400 font-['Orbitron'] font-bold text-xs">CHECK B</span>
                    </div>
                  </div>
                  <div className="neon-border p-3 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300">
                    <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                    <div className="flex justify-between items-center">
                      <span className="text-cyan-300 font-['Exo_2'] text-sm">Server space cooling pump</span>
                      <span className="text-blue-400 font-['Orbitron'] font-bold text-xs">CHECK A</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Anomaly Detection & System Recommendations */}
            <div className="space-y-6">
              {/* Anomaly Detection */}
              <div>
                <h3 className="text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase">
                  Anomaly Detection
                </h3>
                <div className="space-y-3">
                  {anomalies.slice(0, 4).map((anomaly) => {
                    const getConfidenceColor = (confidence: number) => {
                      if (confidence >= 90) return 'text-red-400';
                      if (confidence >= 70) return 'text-yellow-400';
                      return 'text-green-400';
                    };

                    const getConfidenceBorder = (confidence: number) => {
                      if (confidence >= 90) return 'border-red-400';
                      if (confidence >= 70) return 'border-yellow-400';
                      return 'border-green-400';
                    };

                    const getIcon = (type: string) => {
                      switch (type) {
                        case 'power':
                          return '⚡';
                        case 'network':
                          return '🌐';
                        case 'temperature':
                          return '🌡️';
                        default:
                          return '⚠️';
                      }
                    };

                    return (
                      <div
                        key={anomaly.id}
                        className={`neon-border ${getConfidenceBorder(anomaly.confidence)} p-3 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300`}
                      >
                        <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                        <div className="flex justify-between items-center">
                          <div className="flex items-center space-x-3">
                            <span className="text-lg">{getIcon(anomaly.type)}</span>
                            <div>
                              <div className="text-cyan-300 font-['Exo_2'] text-sm">{anomaly.description}</div>
                              <div className="text-xs text-cyan-500 font-['Exo_2']">{anomaly.location}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className={`text-sm font-bold ${getConfidenceColor(anomaly.confidence)} font-['Orbitron']`}>
                              {anomaly.confidence}%
                            </div>
                            <div className="text-xs text-cyan-500 font-['Exo_2']">Confidence</div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* System Recommendations */}
              <div>
                <h3 className="text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase">
                  System Recommendations
                </h3>
                <div className="space-y-3">
                  <div className="neon-border p-3 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300">
                    <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                    <div className="flex justify-between items-center">
                      <span className="text-cyan-300 font-['Exo_2'] text-sm">Optimize CRAC parameters Sector A</span>
                      <span className="text-green-400 font-['Orbitron'] font-bold text-xs">●</span>
                    </div>
                  </div>
                  <div className="neon-border p-3 rounded-lg relative overflow-hidden hover:neon-glow transition-all duration-300">
                    <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                    <div className="flex justify-between items-center">
                      <span className="text-cyan-300 font-['Exo_2'] text-sm">Investigate thermal sensors Rack M</span>
                      <span className="text-yellow-400 font-['Orbitron'] font-bold text-xs">●</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Status Footer */}
        <div className="neon-border rounded-lg p-6 relative overflow-hidden matrix-bg">
          <div className="scan-line"></div>
          <div className="flex justify-between items-center text-sm relative z-10">
            <div className="text-cyan-400 font-['Exo_2']">
              <span className="text-xs tracking-wider uppercase">Last Neural Sync:</span>
              <div className="hologram-text font-['Orbitron'] text-lg">{currentTime.toLocaleTimeString()}</div>
            </div>
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-3">
                <div className="w-4 h-4 bg-green-400 rounded-full animate-pulse pulse-glow"></div>
                <span className="text-cyan-300 font-['Exo_2'] tracking-wider">ALL SYSTEMS OPERATIONAL</span>
              </div>
              <div className="text-cyan-400 font-['Exo_2']">
                <span className="text-xs tracking-wider uppercase">Data Stream:</span>
                <div className="hologram-text font-['Orbitron']">LIVE</div>
              </div>
              <div className="flex items-center space-x-3">
                <Cpu className="w-5 h-5 text-cyan-400 animate-pulse" />
                <span className="text-cyan-300 font-['Exo_2']">
                  <span className="text-xs tracking-wider uppercase">Neural Nodes:</span>
                  <div className="hologram-text font-['Orbitron']">{heatmapData.length}</div>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnifiedDataCenterDashboard;
