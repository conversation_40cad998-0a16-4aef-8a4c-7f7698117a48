{"ast": null, "code": "var getAllKeys = require('./_getAllKeys');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1;\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * A specialized version of `baseIsEqualDeep` for objects with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\nfunction equalObjects(object, other, bitmask, customizer, equalFunc, stack) {\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,\n    objProps = getAllKeys(object),\n    objLength = objProps.length,\n    othProps = getAllKeys(other),\n    othLength = othProps.length;\n  if (objLength != othLength && !isPartial) {\n    return false;\n  }\n  var index = objLength;\n  while (index--) {\n    var key = objProps[index];\n    if (!(isPartial ? key in other : hasOwnProperty.call(other, key))) {\n      return false;\n    }\n  }\n  // Check that cyclic values are equal.\n  var objStacked = stack.get(object);\n  var othStacked = stack.get(other);\n  if (objStacked && othStacked) {\n    return objStacked == other && othStacked == object;\n  }\n  var result = true;\n  stack.set(object, other);\n  stack.set(other, object);\n  var skipCtor = isPartial;\n  while (++index < objLength) {\n    key = objProps[index];\n    var objValue = object[key],\n      othValue = other[key];\n    if (customizer) {\n      var compared = isPartial ? customizer(othValue, objValue, key, other, object, stack) : customizer(objValue, othValue, key, object, other, stack);\n    }\n    // Recursively compare objects (susceptible to call stack limits).\n    if (!(compared === undefined ? objValue === othValue || equalFunc(objValue, othValue, bitmask, customizer, stack) : compared)) {\n      result = false;\n      break;\n    }\n    skipCtor || (skipCtor = key == 'constructor');\n  }\n  if (result && !skipCtor) {\n    var objCtor = object.constructor,\n      othCtor = other.constructor;\n\n    // Non `Object` object instances with different constructors are not equal.\n    if (objCtor != othCtor && 'constructor' in object && 'constructor' in other && !(typeof objCtor == 'function' && objCtor instanceof objCtor && typeof othCtor == 'function' && othCtor instanceof othCtor)) {\n      result = false;\n    }\n  }\n  stack['delete'](object);\n  stack['delete'](other);\n  return result;\n}\nmodule.exports = equalObjects;", "map": {"version": 3, "names": ["getAllKeys", "require", "COMPARE_PARTIAL_FLAG", "objectProto", "Object", "prototype", "hasOwnProperty", "equalObjects", "object", "other", "bitmask", "customizer", "equalFunc", "stack", "isPartial", "objProps", "obj<PERSON><PERSON><PERSON>", "length", "othProps", "oth<PERSON><PERSON><PERSON>", "index", "key", "call", "objStacked", "get", "othStacked", "result", "set", "skip<PERSON><PERSON>", "objValue", "othValue", "compared", "undefined", "objCtor", "constructor", "othCtor", "module", "exports"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/node_modules/lodash/_equalObjects.js"], "sourcesContent": ["var getAllKeys = require('./_getAllKeys');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1;\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * A specialized version of `baseIsEqualDeep` for objects with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\nfunction equalObjects(object, other, bitmask, customizer, equalFunc, stack) {\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,\n      objProps = getAllKeys(object),\n      objLength = objProps.length,\n      othProps = getAllKeys(other),\n      othLength = othProps.length;\n\n  if (objLength != othLength && !isPartial) {\n    return false;\n  }\n  var index = objLength;\n  while (index--) {\n    var key = objProps[index];\n    if (!(isPartial ? key in other : hasOwnProperty.call(other, key))) {\n      return false;\n    }\n  }\n  // Check that cyclic values are equal.\n  var objStacked = stack.get(object);\n  var othStacked = stack.get(other);\n  if (objStacked && othStacked) {\n    return objStacked == other && othStacked == object;\n  }\n  var result = true;\n  stack.set(object, other);\n  stack.set(other, object);\n\n  var skipCtor = isPartial;\n  while (++index < objLength) {\n    key = objProps[index];\n    var objValue = object[key],\n        othValue = other[key];\n\n    if (customizer) {\n      var compared = isPartial\n        ? customizer(othValue, objValue, key, other, object, stack)\n        : customizer(objValue, othValue, key, object, other, stack);\n    }\n    // Recursively compare objects (susceptible to call stack limits).\n    if (!(compared === undefined\n          ? (objValue === othValue || equalFunc(objValue, othValue, bitmask, customizer, stack))\n          : compared\n        )) {\n      result = false;\n      break;\n    }\n    skipCtor || (skipCtor = key == 'constructor');\n  }\n  if (result && !skipCtor) {\n    var objCtor = object.constructor,\n        othCtor = other.constructor;\n\n    // Non `Object` object instances with different constructors are not equal.\n    if (objCtor != othCtor &&\n        ('constructor' in object && 'constructor' in other) &&\n        !(typeof objCtor == 'function' && objCtor instanceof objCtor &&\n          typeof othCtor == 'function' && othCtor instanceof othCtor)) {\n      result = false;\n    }\n  }\n  stack['delete'](object);\n  stack['delete'](other);\n  return result;\n}\n\nmodule.exports = equalObjects;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAEzC;AACA,IAAIC,oBAAoB,GAAG,CAAC;;AAE5B;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAE;EAC1E,IAAIC,SAAS,GAAGJ,OAAO,GAAGR,oBAAoB;IAC1Ca,QAAQ,GAAGf,UAAU,CAACQ,MAAM,CAAC;IAC7BQ,SAAS,GAAGD,QAAQ,CAACE,MAAM;IAC3BC,QAAQ,GAAGlB,UAAU,CAACS,KAAK,CAAC;IAC5BU,SAAS,GAAGD,QAAQ,CAACD,MAAM;EAE/B,IAAID,SAAS,IAAIG,SAAS,IAAI,CAACL,SAAS,EAAE;IACxC,OAAO,KAAK;EACd;EACA,IAAIM,KAAK,GAAGJ,SAAS;EACrB,OAAOI,KAAK,EAAE,EAAE;IACd,IAAIC,GAAG,GAAGN,QAAQ,CAACK,KAAK,CAAC;IACzB,IAAI,EAAEN,SAAS,GAAGO,GAAG,IAAIZ,KAAK,GAAGH,cAAc,CAACgB,IAAI,CAACb,KAAK,EAAEY,GAAG,CAAC,CAAC,EAAE;MACjE,OAAO,KAAK;IACd;EACF;EACA;EACA,IAAIE,UAAU,GAAGV,KAAK,CAACW,GAAG,CAAChB,MAAM,CAAC;EAClC,IAAIiB,UAAU,GAAGZ,KAAK,CAACW,GAAG,CAACf,KAAK,CAAC;EACjC,IAAIc,UAAU,IAAIE,UAAU,EAAE;IAC5B,OAAOF,UAAU,IAAId,KAAK,IAAIgB,UAAU,IAAIjB,MAAM;EACpD;EACA,IAAIkB,MAAM,GAAG,IAAI;EACjBb,KAAK,CAACc,GAAG,CAACnB,MAAM,EAAEC,KAAK,CAAC;EACxBI,KAAK,CAACc,GAAG,CAAClB,KAAK,EAAED,MAAM,CAAC;EAExB,IAAIoB,QAAQ,GAAGd,SAAS;EACxB,OAAO,EAAEM,KAAK,GAAGJ,SAAS,EAAE;IAC1BK,GAAG,GAAGN,QAAQ,CAACK,KAAK,CAAC;IACrB,IAAIS,QAAQ,GAAGrB,MAAM,CAACa,GAAG,CAAC;MACtBS,QAAQ,GAAGrB,KAAK,CAACY,GAAG,CAAC;IAEzB,IAAIV,UAAU,EAAE;MACd,IAAIoB,QAAQ,GAAGjB,SAAS,GACpBH,UAAU,CAACmB,QAAQ,EAAED,QAAQ,EAAER,GAAG,EAAEZ,KAAK,EAAED,MAAM,EAAEK,KAAK,CAAC,GACzDF,UAAU,CAACkB,QAAQ,EAAEC,QAAQ,EAAET,GAAG,EAAEb,MAAM,EAAEC,KAAK,EAAEI,KAAK,CAAC;IAC/D;IACA;IACA,IAAI,EAAEkB,QAAQ,KAAKC,SAAS,GACnBH,QAAQ,KAAKC,QAAQ,IAAIlB,SAAS,CAACiB,QAAQ,EAAEC,QAAQ,EAAEpB,OAAO,EAAEC,UAAU,EAAEE,KAAK,CAAC,GACnFkB,QAAQ,CACX,EAAE;MACLL,MAAM,GAAG,KAAK;MACd;IACF;IACAE,QAAQ,KAAKA,QAAQ,GAAGP,GAAG,IAAI,aAAa,CAAC;EAC/C;EACA,IAAIK,MAAM,IAAI,CAACE,QAAQ,EAAE;IACvB,IAAIK,OAAO,GAAGzB,MAAM,CAAC0B,WAAW;MAC5BC,OAAO,GAAG1B,KAAK,CAACyB,WAAW;;IAE/B;IACA,IAAID,OAAO,IAAIE,OAAO,IACjB,aAAa,IAAI3B,MAAM,IAAI,aAAa,IAAIC,KAAM,IACnD,EAAE,OAAOwB,OAAO,IAAI,UAAU,IAAIA,OAAO,YAAYA,OAAO,IAC1D,OAAOE,OAAO,IAAI,UAAU,IAAIA,OAAO,YAAYA,OAAO,CAAC,EAAE;MACjET,MAAM,GAAG,KAAK;IAChB;EACF;EACAb,KAAK,CAAC,QAAQ,CAAC,CAACL,MAAM,CAAC;EACvBK,KAAK,CAAC,QAAQ,CAAC,CAACJ,KAAK,CAAC;EACtB,OAAOiB,MAAM;AACf;AAEAU,MAAM,CAACC,OAAO,GAAG9B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}