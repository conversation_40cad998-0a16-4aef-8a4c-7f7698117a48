{"ast": null, "code": "var isSymbol = require('./isSymbol');\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/**\n * Converts `value` to a string key if it's not a string or symbol.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {string|symbol} Returns the key.\n */\nfunction toKey(value) {\n  if (typeof value == 'string' || isSymbol(value)) {\n    return value;\n  }\n  var result = value + '';\n  return result == '0' && 1 / value == -INFINITY ? '-0' : result;\n}\nmodule.exports = toKey;", "map": {"version": 3, "names": ["isSymbol", "require", "INFINITY", "to<PERSON><PERSON>", "value", "result", "module", "exports"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/node_modules/lodash/_toKey.js"], "sourcesContent": ["var isSymbol = require('./isSymbol');\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/**\n * Converts `value` to a string key if it's not a string or symbol.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {string|symbol} Returns the key.\n */\nfunction toKey(value) {\n  if (typeof value == 'string' || isSymbol(value)) {\n    return value;\n  }\n  var result = (value + '');\n  return (result == '0' && (1 / value) == -INFINITY) ? '-0' : result;\n}\n\nmodule.exports = toKey;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA,IAAIC,QAAQ,GAAG,CAAC,GAAG,CAAC;;AAEpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,KAAK,EAAE;EACpB,IAAI,OAAOA,KAAK,IAAI,QAAQ,IAAIJ,QAAQ,CAACI,KAAK,CAAC,EAAE;IAC/C,OAAOA,KAAK;EACd;EACA,IAAIC,MAAM,GAAID,KAAK,GAAG,EAAG;EACzB,OAAQC,MAAM,IAAI,GAAG,IAAK,CAAC,GAAGD,KAAK,IAAK,CAACF,QAAQ,GAAI,IAAI,GAAGG,MAAM;AACpE;AAEAC,MAAM,CAACC,OAAO,GAAGJ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}