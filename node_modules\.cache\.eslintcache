[{"D:\\00-WKYap\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\src\\index.tsx": "1", "D:\\00-WKYap\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\src\\App.tsx": "2", "D:\\00-WKYap\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\src\\components\\UnifiedDataCenterDashboard.tsx": "3", "D:\\00-WKYap\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\src\\components\\ThemeSelector.tsx": "4", "D:\\00-WKYap\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\src\\components\\GlassmorphismDataCenterDashboard.tsx": "5"}, {"size": 277, "mtime": 1754124524847, "results": "6", "hashOfConfig": "7"}, {"size": 225, "mtime": 1754129664260, "results": "8", "hashOfConfig": "7"}, {"size": 55328, "mtime": 1754128632865, "results": "9", "hashOfConfig": "7"}, {"size": 3711, "mtime": 1754129675306, "results": "10", "hashOfConfig": "7"}, {"size": 29033, "mtime": 1754129818046, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ukvcxy", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\00-W<PERSON><PERSON>ap\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\src\\index.tsx", [], [], "D:\\00-W<PERSON><PERSON><PERSON>\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\src\\App.tsx", [], [], "D:\\00-W<PERSON>Yap\\12-<PERSON>Apps-AgumentCode\\01-Unified IPS Dashboard\\src\\components\\UnifiedDataCenterDashboard.tsx", ["27", "28", "29", "30", "31", "32", "33", "34", "35", "36"], [], "D:\\00-W<PERSON>Yap\\12-<PERSON>Apps-AgumentCode\\01-Unified IPS Dashboard\\src\\components\\ThemeSelector.tsx", [], [], "D:\\00-W<PERSON>Yap\\12-<PERSON>Apps-AgumentCode\\01-Unified IPS Dashboard\\src\\components\\GlassmorphismDataCenterDashboard.tsx", ["37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47"], [], {"ruleId": "48", "severity": 1, "message": "49", "line": 3, "column": 25, "nodeType": "50", "messageId": "51", "endLine": 3, "endColumn": 36}, {"ruleId": "48", "severity": 1, "message": "52", "line": 3, "column": 38, "nodeType": "50", "messageId": "51", "endLine": 3, "endColumn": 41}, {"ruleId": "48", "severity": 1, "message": "53", "line": 3, "column": 43, "nodeType": "50", "messageId": "51", "endLine": 3, "endColumn": 49}, {"ruleId": "48", "severity": 1, "message": "54", "line": 3, "column": 51, "nodeType": "50", "messageId": "51", "endLine": 3, "endColumn": 57}, {"ruleId": "48", "severity": 1, "message": "55", "line": 3, "column": 59, "nodeType": "50", "messageId": "51", "endLine": 3, "endColumn": 69}, {"ruleId": "48", "severity": 1, "message": "56", "line": 3, "column": 71, "nodeType": "50", "messageId": "51", "endLine": 3, "endColumn": 79}, {"ruleId": "48", "severity": 1, "message": "57", "line": 3, "column": 107, "nodeType": "50", "messageId": "51", "endLine": 3, "endColumn": 115}, {"ruleId": "48", "severity": 1, "message": "58", "line": 3, "column": 122, "nodeType": "50", "messageId": "51", "endLine": 3, "endColumn": 131}, {"ruleId": "48", "severity": 1, "message": "59", "line": 3, "column": 133, "nodeType": "50", "messageId": "51", "endLine": 3, "endColumn": 137}, {"ruleId": "48", "severity": 1, "message": "60", "line": 348, "column": 9, "nodeType": "50", "messageId": "51", "endLine": 348, "endColumn": 22}, {"ruleId": "48", "severity": 1, "message": "61", "line": 2, "column": 10, "nodeType": "50", "messageId": "51", "endLine": 2, "endColumn": 19}, {"ruleId": "48", "severity": 1, "message": "62", "line": 2, "column": 21, "nodeType": "50", "messageId": "51", "endLine": 2, "endColumn": 25}, {"ruleId": "48", "severity": 1, "message": "52", "line": 3, "column": 38, "nodeType": "50", "messageId": "51", "endLine": 3, "endColumn": 41}, {"ruleId": "48", "severity": 1, "message": "54", "line": 3, "column": 51, "nodeType": "50", "messageId": "51", "endLine": 3, "endColumn": 57}, {"ruleId": "48", "severity": 1, "message": "56", "line": 3, "column": 71, "nodeType": "50", "messageId": "51", "endLine": 3, "endColumn": 79}, {"ruleId": "48", "severity": 1, "message": "63", "line": 3, "column": 87, "nodeType": "50", "messageId": "51", "endLine": 3, "endColumn": 93}, {"ruleId": "48", "severity": 1, "message": "64", "line": 3, "column": 101, "nodeType": "50", "messageId": "51", "endLine": 3, "endColumn": 105}, {"ruleId": "48", "severity": 1, "message": "57", "line": 3, "column": 107, "nodeType": "50", "messageId": "51", "endLine": 3, "endColumn": 115}, {"ruleId": "48", "severity": 1, "message": "58", "line": 3, "column": 122, "nodeType": "50", "messageId": "51", "endLine": 3, "endColumn": 131}, {"ruleId": "48", "severity": 1, "message": "59", "line": 3, "column": 133, "nodeType": "50", "messageId": "51", "endLine": 3, "endColumn": 137}, {"ruleId": "48", "severity": 1, "message": "65", "line": 190, "column": 10, "nodeType": "50", "messageId": "51", "endLine": 190, "endColumn": 19}, "@typescript-eslint/no-unused-vars", "'Thermometer' is defined but never used.", "Identifier", "unusedVar", "'Zap' is defined but never used.", "'Server' is defined but never used.", "'Shield' is defined but never used.", "'TrendingUp' is defined but never used.", "'Settings' is defined but never used.", "'Activity' is defined but never used.", "'HardDrive' is defined but never used.", "'Wifi' is defined but never used.", "'getAlarmColor' is assigned a value but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'Search' is defined but never used.", "'Menu' is defined but never used.", "'anomalies' is assigned a value but never used."]