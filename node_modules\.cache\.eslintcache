[{"D:\\00-WKYap\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\src\\index.tsx": "1", "D:\\00-WKYap\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\src\\App.tsx": "2", "D:\\00-WKYap\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\src\\components\\UnifiedDataCenterDashboard.tsx": "3"}, {"size": 277, "mtime": 1754124524847, "results": "4", "hashOfConfig": "5"}, {"size": 264, "mtime": 1754124554143, "results": "6", "hashOfConfig": "5"}, {"size": 39455, "mtime": 1754127619047, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ukvcxy", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\00-W<PERSON><PERSON>ap\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\src\\index.tsx", [], [], "D:\\00-W<PERSON><PERSON><PERSON>\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\src\\App.tsx", [], [], "D:\\00-W<PERSON>Yap\\12-<PERSON>Apps-AgumentCode\\01-Unified IPS Dashboard\\src\\components\\UnifiedDataCenterDashboard.tsx", ["17", "18", "19", "20", "21", "22", "23", "24", "25", "26"], [], {"ruleId": "27", "severity": 1, "message": "28", "line": 3, "column": 25, "nodeType": "29", "messageId": "30", "endLine": 3, "endColumn": 36}, {"ruleId": "27", "severity": 1, "message": "31", "line": 3, "column": 38, "nodeType": "29", "messageId": "30", "endLine": 3, "endColumn": 41}, {"ruleId": "27", "severity": 1, "message": "32", "line": 3, "column": 43, "nodeType": "29", "messageId": "30", "endLine": 3, "endColumn": 49}, {"ruleId": "27", "severity": 1, "message": "33", "line": 3, "column": 51, "nodeType": "29", "messageId": "30", "endLine": 3, "endColumn": 57}, {"ruleId": "27", "severity": 1, "message": "34", "line": 3, "column": 59, "nodeType": "29", "messageId": "30", "endLine": 3, "endColumn": 69}, {"ruleId": "27", "severity": 1, "message": "35", "line": 3, "column": 71, "nodeType": "29", "messageId": "30", "endLine": 3, "endColumn": 79}, {"ruleId": "27", "severity": 1, "message": "36", "line": 3, "column": 107, "nodeType": "29", "messageId": "30", "endLine": 3, "endColumn": 115}, {"ruleId": "27", "severity": 1, "message": "37", "line": 3, "column": 122, "nodeType": "29", "messageId": "30", "endLine": 3, "endColumn": 131}, {"ruleId": "27", "severity": 1, "message": "38", "line": 3, "column": 133, "nodeType": "29", "messageId": "30", "endLine": 3, "endColumn": 137}, {"ruleId": "27", "severity": 1, "message": "39", "line": 347, "column": 9, "nodeType": "29", "messageId": "30", "endLine": 347, "endColumn": 22}, "@typescript-eslint/no-unused-vars", "'Thermometer' is defined but never used.", "Identifier", "unusedVar", "'Zap' is defined but never used.", "'Server' is defined but never used.", "'Shield' is defined but never used.", "'TrendingUp' is defined but never used.", "'Settings' is defined but never used.", "'Activity' is defined but never used.", "'HardDrive' is defined but never used.", "'Wifi' is defined but never used.", "'getAlarmColor' is assigned a value but never used."]