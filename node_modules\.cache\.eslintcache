[{"D:\\00-WKYap\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\src\\index.tsx": "1", "D:\\00-WKYap\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\src\\App.tsx": "2", "D:\\00-WKYap\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\src\\components\\UnifiedDataCenterDashboard.tsx": "3"}, {"size": 277, "mtime": 1754124524847, "results": "4", "hashOfConfig": "5"}, {"size": 264, "mtime": 1754124554143, "results": "6", "hashOfConfig": "5"}, {"size": 30009, "mtime": 1754126890523, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ukvcxy", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\00-W<PERSON><PERSON>ap\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\src\\index.tsx", [], [], "D:\\00-W<PERSON><PERSON><PERSON>\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\src\\App.tsx", [], [], "D:\\00-W<PERSON>Yap\\12-<PERSON>Apps-AgumentCode\\01-Unified IPS Dashboard\\src\\components\\UnifiedDataCenterDashboard.tsx", ["17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29"], [], {"ruleId": "30", "severity": 1, "message": "31", "line": 2, "column": 10, "nodeType": "32", "messageId": "33", "endLine": 2, "endColumn": 19}, {"ruleId": "30", "severity": 1, "message": "34", "line": 2, "column": 21, "nodeType": "32", "messageId": "33", "endLine": 2, "endColumn": 25}, {"ruleId": "30", "severity": 1, "message": "35", "line": 3, "column": 25, "nodeType": "32", "messageId": "33", "endLine": 3, "endColumn": 36}, {"ruleId": "30", "severity": 1, "message": "36", "line": 3, "column": 38, "nodeType": "32", "messageId": "33", "endLine": 3, "endColumn": 41}, {"ruleId": "30", "severity": 1, "message": "37", "line": 3, "column": 43, "nodeType": "32", "messageId": "33", "endLine": 3, "endColumn": 49}, {"ruleId": "30", "severity": 1, "message": "38", "line": 3, "column": 51, "nodeType": "32", "messageId": "33", "endLine": 3, "endColumn": 57}, {"ruleId": "30", "severity": 1, "message": "39", "line": 3, "column": 59, "nodeType": "32", "messageId": "33", "endLine": 3, "endColumn": 69}, {"ruleId": "30", "severity": 1, "message": "40", "line": 3, "column": 71, "nodeType": "32", "messageId": "33", "endLine": 3, "endColumn": 79}, {"ruleId": "30", "severity": 1, "message": "41", "line": 3, "column": 107, "nodeType": "32", "messageId": "33", "endLine": 3, "endColumn": 115}, {"ruleId": "30", "severity": 1, "message": "42", "line": 3, "column": 122, "nodeType": "32", "messageId": "33", "endLine": 3, "endColumn": 131}, {"ruleId": "30", "severity": 1, "message": "43", "line": 3, "column": 133, "nodeType": "32", "messageId": "33", "endLine": 3, "endColumn": 137}, {"ruleId": "30", "severity": 1, "message": "44", "line": 156, "column": 10, "nodeType": "32", "messageId": "33", "endLine": 156, "endColumn": 19}, {"ruleId": "30", "severity": 1, "message": "45", "line": 347, "column": 9, "nodeType": "32", "messageId": "33", "endLine": 347, "endColumn": 22}, "@typescript-eslint/no-unused-vars", "'LineChart' is defined but never used.", "Identifier", "unusedVar", "'Line' is defined but never used.", "'Thermometer' is defined but never used.", "'Zap' is defined but never used.", "'Server' is defined but never used.", "'Shield' is defined but never used.", "'TrendingUp' is defined but never used.", "'Settings' is defined but never used.", "'Activity' is defined but never used.", "'HardDrive' is defined but never used.", "'Wifi' is defined but never used.", "'anomalies' is assigned a value but never used.", "'getAlarmColor' is assigned a value but never used."]