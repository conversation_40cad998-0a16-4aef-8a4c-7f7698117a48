{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst UserCheck2 = createLucideIcon(\"UserCheck2\", [[\"path\", {\n  d: \"M14 19a6 6 0 0 0-12 0\",\n  key: \"vej9p1\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"9\",\n  r: \"4\",\n  key: \"143rtg\"\n}], [\"polyline\", {\n  points: \"16 11 18 13 22 9\",\n  key: \"1pwet4\"\n}]]);\nexport { UserCheck2 as default };", "map": {"version": 3, "names": ["UserCheck2", "createLucideIcon", "d", "key", "cx", "cy", "r", "points"], "sources": ["D:\\00-WKYap\\12-AIApps-AgumentCode\\01-Unified IPS Dashboard\\node_modules\\lucide-react\\src\\icons\\user-check-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name UserCheck2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgMTlhNiA2IDAgMCAwLTEyIDAiIC8+CiAgPGNpcmNsZSBjeD0iOCIgY3k9IjkiIHI9IjQiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTYgMTEgMTggMTMgMjIgOSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/user-check-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UserCheck2 = createLucideIcon('UserCheck2', [\n  ['path', { d: 'M14 19a6 6 0 0 0-12 0', key: 'vej9p1' }],\n  ['circle', { cx: '8', cy: '9', r: '4', key: '143rtg' }],\n  ['polyline', { points: '16 11 18 13 22 9', key: '1pwet4' }],\n]);\n\nexport default UserCheck2;\n"], "mappings": ";;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACtD,CAAC,UAAY;EAAEI,MAAA,EAAQ,kBAAoB;EAAAJ,GAAA,EAAK;AAAA,CAAU,EAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}