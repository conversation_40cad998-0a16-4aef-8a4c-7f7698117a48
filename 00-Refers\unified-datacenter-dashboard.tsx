import React, { useState, useEffect } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import { AlertTriangle, Thermometer, Zap, Server, Shield, TrendingUp, Settings, Bell, Search, User, Menu } from 'lucide-react';

const UnifiedDataCenterDashboard = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [selectedRack, setSelectedRack] = useState(null);
  const [kpiData, setKpiData] = useState({
    pue: 1.45,
    itLoad: 1.1,
    coolingLoad: 1.1,
    trialPower: 2.4,
    upsPower: 1.11,
    pduPower: 1.5,
    totalAlarms: 4,
    activeAlarms: 2
  });
  const [alarms, setAlarms] = useState([]);
  const [heatmapData, setHeatmapData] = useState([]);
  const [anomalies, setAnomalies] = useState([]);

  // Generate realistic KPI data
  const generateKPIData = () => {
    const now = new Date();
    const hour = now.getHours();
    const minute = now.getMinutes();
    const timeVariation = Math.sin((hour * 60 + minute) * Math.PI / 720);
    
    return {
      pue: 1.42 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.02,
      itLoad: 1.08 + timeVariation * 0.15 + (Math.random() - 0.5) * 0.03,
      coolingLoad: 1.1 + timeVariation * 0.08 + (Math.random() - 0.5) * 0.02,
      trialPower: 2.35 + timeVariation * 0.2 + (Math.random() - 0.5) * 0.05,
      upsPower: 1.1 + (Math.random() - 0.5) * 0.01,
      pduPower: 1.48 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.02,
      totalAlarms: Math.floor(3 + Math.random() * 5),
      activeAlarms: Math.floor(1 + Math.random() * 3)
    };
  };

  // Generate alarm data
  const generateAlarmData = () => {
    const alarmTypes = ['Critical', 'Warning', 'Info'];
    const locations = ['Rack A1', 'Rack B3', 'UPS Room', 'Cooling Unit 2', 'Switch Core', 'PDU Zone C'];
    const messages = {
      Critical: ['Temperature Threshold Exceeded', 'Power Supply Failure', 'Cooling System Down'],
      Warning: ['High Humidity Detected', 'UPS Battery Low', 'Network Latency High'],
      Info: ['Scheduled Maintenance', 'System Update Complete', 'Backup Process Started']
    };
    
    const numAlarms = 4 + Math.floor(Math.random() * 3);
    const generatedAlarms = [];
    
    for (let i = 0; i < numAlarms; i++) {
      const type = alarmTypes[Math.floor(Math.random() * alarmTypes.length)];
      const location = locations[Math.floor(Math.random() * locations.length)];
      const message = messages[type][Math.floor(Math.random() * messages[type].length)];
      const time = new Date(Date.now() - Math.random() * 3600000);
      const status = Math.random() > 0.3 ? 'Active' : 'Resolved';
      
      generatedAlarms.push({
        id: i + 1,
        type,
        message,
        location,
        time: time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
        status,
        severity: type === 'Critical' ? 3 : type === 'Warning' ? 2 : 1
      });
    }
    
    return generatedAlarms.sort((a, b) => b.severity - a.severity);
  };

  // Generate heatmap data
  const generateHeatmapData = () => {
    const data = [];
    const baseTemp = 22;
    const now = Date.now();
    
    for (let row = 0; row < 8; row++) {
      for (let col = 0; col < 10; col++) {
        const distanceFromCenter = Math.sqrt(Math.pow(col - 4.5, 2) + Math.pow(row - 3.5, 2));
        const hotSpotInfluence = Math.sin(now / 10000 + row * col) * 2;
        const loadVariation = Math.sin(now / 5000 + col * 0.5) * 1.5;
        
        const temperature = baseTemp + 
          (distanceFromCenter < 2 ? -1 : distanceFromCenter > 5 ? 2 : 0) +
          hotSpotInfluence + 
          (Math.random() - 0.5) * 1.5;
        
        const power = 60 + loadVariation + (Math.random() - 0.5) * 30;
        
        data.push({
          x: col,
          y: row,
          temperature: Math.max(18, Math.min(32, temperature)),
          power: Math.max(20, Math.min(100, power)),
          id: `R${row + 1}C${col + 1}`,
          status: temperature > 28 ? 'critical' : temperature > 25 ? 'warning' : 'normal'
        });
      }
    }
    
    return data;
  };

  // Generate anomaly data
  const generateAnomalies = () => {
    const anomalyTypes = [
      { icon: 'power', description: 'Unusual power consumption pattern' },
      { icon: 'temperature', description: 'Temperature gradient anomaly' },
      { icon: 'network', description: 'Network bandwidth surge detected' },
      { icon: 'cooling', description: 'HVAC performance degradation' }
    ];
    
    const locations = ['Rack A3-A5', 'Zone B, Rows 3-4', 'Core switches', 'Cooling Unit 1'];
    const numAnomalies = 2 + Math.floor(Math.random() * 3);
    const anomalies = [];
    
    for (let i = 0; i < numAnomalies; i++) {
      const anomaly = anomalyTypes[Math.floor(Math.random() * anomalyTypes.length)];
      const location = locations[Math.floor(Math.random() * locations.length)];
      const confidence = 75 + Math.floor(Math.random() * 25);
      
      anomalies.push({
        id: i + 1,
        ...anomaly,
        location,
        confidence,
        severity: confidence > 90 ? 'critical' : confidence > 80 ? 'warning' : 'info'
      });
    }
    
    return anomalies.sort((a, b) => b.confidence - a.confidence);
  };

  // Generate time series data
  const generateTimeSeriesData = () => {
    const data = [];
    const now = new Date();
    
    for (let i = 23; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 60 * 60 * 1000);
      const hour = time.getHours();
      
      const loadFactor = 0.8 + 0.3 * Math.sin((hour - 6) * Math.PI / 12);
      const coolingFactor = 1 + 0.2 * Math.sin((hour - 14) * Math.PI / 12);
      
      data.push({
        time: time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
        power: 1.42 * loadFactor + (Math.random() - 0.5) * 0.05,
        cooling: 1.1 * coolingFactor + (Math.random() - 0.5) * 0.03,
        temperature: 22.5 + 2 * Math.sin((hour - 14) * Math.PI / 12) + (Math.random() - 0.5) * 0.8,
        effectiveness: 85 + 10 * Math.sin((hour - 10) * Math.PI / 12) + (Math.random() - 0.5) * 2
      });
    }
    
    return data;
  };

  const [timeSeriesData, setTimeSeriesData] = useState(generateTimeSeriesData());

  // Initialize data
  useEffect(() => {
    setAlarms(generateAlarmData());
    setHeatmapData(generateHeatmapData());
    setAnomalies(generateAnomalies());
  }, []);

  // Real-time updates
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
      setKpiData(generateKPIData());
      setTimeSeriesData(generateTimeSeriesData());
    }, 1000);
    
    return () => clearInterval(timer);
  }, []);

  // Update heatmap every 3 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setHeatmapData(generateHeatmapData());
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  // Update alarms every 15 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setAlarms(generateAlarmData());
    }, 15000);
    return () => clearInterval(interval);
  }, []);

  // Update anomalies every 20 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setAnomalies(generateAnomalies());
    }, 20000);
    return () => clearInterval(interval);
  }, []);

  const getTemperatureColor = (temp) => {
    if (temp < 20) return '#4ade80';
    if (temp < 22) return '#84cc16';
    if (temp < 24) return '#eab308';
    if (temp < 26) return '#f97316';
    if (temp < 28) return '#ef4444';
    return '#dc2626';
  };

  const getAlarmColor = (type) => {
    switch (type) {
      case 'Critical': return 'text-red-600 bg-red-50';
      case 'Warning': return 'text-amber-600 bg-amber-50';
      case 'Info': return 'text-blue-600 bg-blue-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const KPIGauge = ({ title, value, unit, max, color = 'blue' }) => {
    const percentage = (value / max) * 100;
    const strokeColor = color === 'green' ? '#10b981' : color === 'red' ? '#ef4444' : '#3b82f6';
    
    return (
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="text-sm text-gray-600 mb-2">{title}</div>
        <div className="flex items-center justify-between">
          <div className="text-2xl font-bold">{value}{unit}</div>
          <div className="w-16 h-16 relative">
            <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
              <path
                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke="#e5e7eb"
                strokeWidth="2"
              />
              <path
                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke={strokeColor}
                strokeWidth="2"
                strokeDasharray={`${percentage}, 100`}
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-xs font-medium">{Math.round(percentage)}%</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Menu className="w-6 h-6 text-gray-500" />
            <h1 className="text-xl font-semibold text-gray-900">Unified Data Center Dashboard</h1>
          </div>
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input 
                type="text" 
                placeholder="Search systems..." 
                className="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <Bell className="w-6 h-6 text-gray-500" />
            <User className="w-6 h-6 text-gray-500" />
          </div>
        </div>
      </header>

      <div className="p-6 space-y-6">
        {/* Top Row - Executive Overview & Energy Console */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Executive Overview */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h2 className="text-lg font-semibold mb-4">Executive Overview</h2>
            
            {/* KPI Cards */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <KPIGauge title="PUE" value={kpiData.pue.toFixed(2)} unit="" max={3} color="green" />
              <KPIGauge title="IT Load" value={kpiData.itLoad.toFixed(1)} unit="M" max={2} color="blue" />
              <KPIGauge title="Cooling Load" value={Math.round(kpiData.coolingLoad * 650)} unit=" RT" max={1000} color="blue" />
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="text-sm text-gray-600 mb-2">Alarms</div>
                <div className="text-2xl font-bold text-red-600">{kpiData.totalAlarms}</div>
                <div className="text-xs text-gray-500">{kpiData.activeAlarms} Active</div>
              </div>
            </div>

            {/* Thermal Effectiveness Chart */}
            <div className="mb-4">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Thermal Effectiveness</h3>
              <div className="h-32">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={timeSeriesData.slice(-12)}>
                    <defs>
                      <linearGradient id="colorEffectiveness" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis domain={[75, 95]} />
                    <Tooltip />
                    <Area type="monotone" dataKey="effectiveness" stroke="#3b82f6" fillOpacity={1} fill="url(#colorEffectiveness)" />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Alarms Table */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">Recent Alarms</h3>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {alarms.slice(0, 4).map((alarm) => (
                  <div key={alarm.id} className={`flex justify-between items-center p-3 rounded-lg ${getAlarmColor(alarm.type)}`}>
                    <div className="flex items-center space-x-3">
                      <AlertTriangle className="w-4 h-4" />
                      <div>
                        <div className="font-medium text-sm">{alarm.message}</div>
                        <div className="text-xs opacity-75">{alarm.location} • {alarm.status}</div>
                      </div>
                    </div>
                    <div className="text-xs">{alarm.time}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Energy Console */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h2 className="text-lg font-semibold mb-4">Energy Console</h2>
            
            {/* Energy Flow Diagram */}
            <div className="mb-6">
              <div className="flex items-center justify-between">
                <div className="text-center">
                  <div className="bg-blue-100 p-3 rounded-lg mb-2">
                    <div className="text-sm text-gray-600">PUE</div>
                    <div className="text-xl font-bold">{kpiData.pue.toFixed(2)}</div>
                  </div>
                </div>
                <div className="flex-1 mx-4">
                  <div className="relative">
                    <div className="h-1 bg-gray-200 rounded"></div>
                    <div className="h-1 bg-blue-500 rounded absolute top-0 left-0" style={{width: `${Math.min(100, kpiData.pue * 33)}%`}}></div>
                  </div>
                  <div className="text-center mt-2">
                    <div className="bg-green-100 p-2 rounded">
                      <div className="text-xs">UPS</div>
                      <div className="font-bold">{kpiData.upsPower.toFixed(2)}W</div>
                    </div>
                  </div>
                </div>
                <div className="text-center">
                  <div className="bg-yellow-100 p-3 rounded-lg mb-2">
                    <div className="text-sm text-gray-600">PDU</div>
                    <div className="text-xl font-bold">{kpiData.pduPower.toFixed(1)}M</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Power Metrics */}
            <div className="grid grid-cols-2 gap-4 mb-6">
              <KPIGauge title="Trial Power" value={kpiData.trialPower.toFixed(1)} unit=" MW" max={5} color="green" />
              <KPIGauge title="Cooling Load" value={kpiData.coolingLoad.toFixed(1)} unit=" MW" max={2} color="blue" />
            </div>

            {/* Power Load Trend */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">Power Load Trend</h3>
              <div className="h-40">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={timeSeriesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis domain={[1.3, 1.6]} />
                    <Tooltip />
                    <Line type="monotone" dataKey="power" stroke="#3b82f6" strokeWidth={2} dot={false} />
                    <Line type="monotone" dataKey="cooling" stroke="#10b981" strokeWidth={2} dot={false} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Row - Asset 360 & Predictive AI Insights */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Asset 360 - Rack Heatmap */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h2 className="text-lg font-semibold mb-4">Asset 360° - Thermal Heatmap</h2>
            
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-600">Data Hall Temperature Distribution</span>
                <div className="flex items-center space-x-2 text-xs">
                  <span>Cool</span>
                  <div className="flex space-x-1">
                    <div className="w-3 h-3 bg-green-400 rounded"></div>
                    <div className="w-3 h-3 bg-yellow-400 rounded"></div>
                    <div className="w-3 h-3 bg-orange-400 rounded"></div>
                    <div className="w-3 h-3 bg-red-400 rounded"></div>
                    <div className="w-3 h-3 bg-red-600 rounded"></div>
                  </div>
                  <span>Hot</span>
                </div>
              </div>
              
              {/* Heatmap Grid */}
              <div className="grid grid-cols-10 gap-1 p-4 bg-gray-50 rounded-lg">
                {heatmapData.map((rack, index) => (
                  <div
                    key={index}
                    className="aspect-square rounded cursor-pointer hover:scale-110 transition-transform"
                    style={{ backgroundColor: getTemperatureColor(rack.temperature) }}
                    onClick={() => setSelectedRack(rack)}
                    title={`${rack.id}: ${rack.temperature.toFixed(1)}°C`}
                  />
                ))}
              </div>
              
              {selectedRack && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                  <div className="font-medium">Rack {selectedRack.id}</div>
                  <div className="text-sm text-gray-600 mt-1">
                    <div>Temperature: <span className="font-medium">{selectedRack.temperature.toFixed(1)}°C</span></div>
                    <div>Power Load: <span className="font-medium">{selectedRack.power.toFixed(1)}%</span></div>
                    <div>Status: <span className={`font-medium ${selectedRack.status === 'critical' ? 'text-red-600' : selectedRack.status === 'warning' ? 'text-yellow-600' : 'text-green-600'}`}>
                      {selectedRack.status.charAt(0).toUpperCase() + selectedRack.status.slice(1)}
                    </span></div>
                    <div className="text-xs text-gray-500 mt-1">Last updated: {currentTime.toLocaleTimeString()}</div>
                  </div>
                </div>
              )}
            </div>

            {/* Capacity Forecast */}
            <div className="mb-4">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Capacity Forecast</h3>
              <div className="h-32">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={timeSeriesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="power" stroke="#8884d8" strokeWidth={2} dot={false} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Power Consumption */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Power Consumption</h3>
              <div className="h-32">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={timeSeriesData}>
                    <defs>
                      <linearGradient id="colorPower" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="cooling" stroke="#10b981" fillOpacity={1} fill="url(#colorPower)" />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>

          {/* Predictive AI Insights */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h2 className="text-lg font-semibold mb-4">Predictive AI Insights</h2>
            
            {/* Cooling Forecast */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-3">Cooling Forecast</h3>
              <div className="h-32 mb-4">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={timeSeriesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis domain={[20, 26]} />
                    <Tooltip />
                    <Line type="monotone" dataKey="temperature" stroke="#f59e0b" strokeWidth={2} dot={false} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
              
              {/* Recommendations */}
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-700">Recommendations</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between items-center p-2 bg-green-50 rounded">
                    <span>Optimize I/O airflows Sector A</span>
                    <span className="text-green-600">Exec A</span>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-yellow-50 rounded">
                    <span>Monitor return chilled water</span>
                    <span className="text-yellow-600">Check B</span>  
                  </div>
                  <div className="flex justify-between items-center p-2 bg-blue-50 rounded">
                    <span>Server space cooling pump</span>
                    <span className="text-blue-600">Check A</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Anomaly Detection */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-3">Anomaly Detection</h3>
              <div className="space-y-3">
                {anomalies.map((anomaly) => {
                  const bgColor = anomaly.severity === 'critical' ? 'bg-red-50' : 
                                  anomaly.severity === 'warning' ? 'bg-yellow-50' : 'bg-blue-50';
                  const textColor = anomaly.severity === 'critical' ? 'text-red-500' : 
                                   anomaly.severity === 'warning' ? 'text-yellow-500' : 'text-blue-500';
                  
                  return (
                    <div key={anomaly.id} className={`flex items-center justify-between p-3 rounded-lg ${bgColor}`}>
                      <div className="flex items-center space-x-3">
                        {anomaly.icon === 'power' && <Zap className={`w-5 h-5 ${textColor}`} />}
                        {anomaly.icon === 'temperature' && <Thermometer className={`w-5 h-5 ${textColor}`} />}
                        {anomaly.icon === 'network' && <TrendingUp className={`w-5 h-5 ${textColor}`} />}
                        {anomaly.icon === 'cooling' && <Server className={`w-5 h-5 ${textColor}`} />}
                        <div>
                          <div className="font-medium text-sm">{anomaly.description}</div>
                          <div className="text-xs text-gray-600">{anomaly.location}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">{anomaly.confidence}%</div>
                        <div className="text-xs text-gray-500">Confidence</div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Additional Recommendations */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">System Recommendations</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between items-center p-2 bg-purple-50 rounded">
                  <span>Optimize CRAC parameters Sector A</span>
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                </div>
                <div className="flex justify-between items-center p-2 bg-indigo-50 rounded">
                  <span>Investigate thermal sensors Rack M</span>
                  <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Status Footer */}
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex justify-between items-center text-sm text-gray-600">
            <div>Last updated: {currentTime.toLocaleTimeString()}</div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>All Systems Operational</span>
              </div>
              <div>Data refresh: Live</div>
              <div className="flex items-center space-x-2">
                <span>Connected devices: {heatmapData.length}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnifiedDataCenterDashboard;