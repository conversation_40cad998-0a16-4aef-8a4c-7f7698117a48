import React, { useState, useEffect } from 'react';
import { <PERSON>Chart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import { AlertTriangle, Thermometer, Zap, Server, Shield, TrendingUp, Settings, Bell, Search, User, Menu, Activity, Cpu, HardDrive, Wifi } from 'lucide-react';

// Futuristic CSS styles
const futuristicStyles = `
  @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap');

  .futuristic-bg {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0a0a0a 100%);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
  }

  .futuristic-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(0, 255, 0, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }

  .neon-border {
    border: 1px solid rgba(0, 255, 255, 0.3);
    box-shadow:
      0 0 10px rgba(0, 255, 255, 0.2),
      inset 0 0 10px rgba(0, 255, 255, 0.1);
    background: rgba(0, 20, 40, 0.8);
    backdrop-filter: blur(10px);
  }

  .neon-glow {
    box-shadow:
      0 0 20px rgba(0, 255, 255, 0.4),
      0 0 40px rgba(0, 255, 255, 0.2),
      inset 0 0 20px rgba(0, 255, 255, 0.1);
  }

  .hologram-text {
    font-family: 'Orbitron', monospace;
    color: #00ffff;
    text-shadow:
      0 0 10px rgba(0, 255, 255, 0.8),
      0 0 20px rgba(0, 255, 255, 0.4),
      0 0 30px rgba(0, 255, 255, 0.2);
  }

  .data-stream {
    background: linear-gradient(90deg, transparent 0%, rgba(0, 255, 255, 0.1) 50%, transparent 100%);
    animation: dataFlow 3s linear infinite;
  }

  @keyframes dataFlow {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  .pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite alternate;
  }

  @keyframes pulseGlow {
    from { box-shadow: 0 0 20px rgba(0, 255, 255, 0.4); }
    to { box-shadow: 0 0 30px rgba(0, 255, 255, 0.8), 0 0 40px rgba(0, 255, 255, 0.4); }
  }

  .matrix-bg {
    background:
      linear-gradient(90deg, transparent 98%, rgba(0, 255, 255, 0.1) 100%),
      linear-gradient(0deg, transparent 98%, rgba(0, 255, 255, 0.1) 100%);
    background-size: 20px 20px;
  }

  .scan-line {
    position: relative;
    overflow: hidden;
  }

  .scan-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ffff, transparent);
    animation: scanLine 3s linear infinite;
  }

  @keyframes scanLine {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(0, 20, 40, 0.5);
    border-radius: 4px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #00ffff, #0080ff);
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.4);
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #00ffff, #00aaff);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.6);
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .hologram-text {
      font-size: 0.9em;
    }

    .neon-border {
      padding: 1rem;
    }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = futuristicStyles;
  document.head.appendChild(styleElement);
}

const UnifiedDataCenterDashboard = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [selectedRack, setSelectedRack] = useState(null);
  const [kpiData, setKpiData] = useState({
    pue: 1.45,
    itLoad: 1.1,
    coolingLoad: 1.1,
    trialPower: 2.4,
    upsPower: 1.11,
    pduPower: 1.5,
    totalAlarms: 4,
    activeAlarms: 2
  });
  const [alarms, setAlarms] = useState([]);
  const [heatmapData, setHeatmapData] = useState([]);
  const [anomalies, setAnomalies] = useState([]);

  // Generate realistic KPI data
  const generateKPIData = () => {
    const now = new Date();
    const hour = now.getHours();
    const minute = now.getMinutes();
    const timeVariation = Math.sin((hour * 60 + minute) * Math.PI / 720);
    
    return {
      pue: 1.42 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.02,
      itLoad: 1.08 + timeVariation * 0.15 + (Math.random() - 0.5) * 0.03,
      coolingLoad: 1.1 + timeVariation * 0.08 + (Math.random() - 0.5) * 0.02,
      trialPower: 2.35 + timeVariation * 0.2 + (Math.random() - 0.5) * 0.05,
      upsPower: 1.1 + (Math.random() - 0.5) * 0.01,
      pduPower: 1.48 + timeVariation * 0.1 + (Math.random() - 0.5) * 0.02,
      totalAlarms: Math.floor(3 + Math.random() * 5),
      activeAlarms: Math.floor(1 + Math.random() * 3)
    };
  };

  // Generate alarm data
  const generateAlarmData = () => {
    const alarmTypes = ['Critical', 'Warning', 'Info'];
    const locations = ['Rack A1', 'Rack B3', 'UPS Room', 'Cooling Unit 2', 'Switch Core', 'PDU Zone C'];
    const messages = {
      Critical: ['Temperature Threshold Exceeded', 'Power Supply Failure', 'Cooling System Down'],
      Warning: ['High Humidity Detected', 'UPS Battery Low', 'Network Latency High'],
      Info: ['Scheduled Maintenance', 'System Update Complete', 'Backup Process Started']
    };
    
    const numAlarms = 4 + Math.floor(Math.random() * 3);
    const generatedAlarms = [];
    
    for (let i = 0; i < numAlarms; i++) {
      const type = alarmTypes[Math.floor(Math.random() * alarmTypes.length)];
      const location = locations[Math.floor(Math.random() * locations.length)];
      const message = messages[type][Math.floor(Math.random() * messages[type].length)];
      const time = new Date(Date.now() - Math.random() * 3600000);
      const status = Math.random() > 0.3 ? 'Active' : 'Resolved';
      
      generatedAlarms.push({
        id: i + 1,
        type,
        message,
        location,
        time: time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
        status,
        severity: type === 'Critical' ? 3 : type === 'Warning' ? 2 : 1
      });
    }
    
    return generatedAlarms.sort((a, b) => b.severity - a.severity);
  };

  // Generate heatmap data
  const generateHeatmapData = () => {
    const data = [];
    const baseTemp = 22;
    const now = Date.now();
    
    for (let row = 0; row < 8; row++) {
      for (let col = 0; col < 10; col++) {
        const distanceFromCenter = Math.sqrt(Math.pow(col - 4.5, 2) + Math.pow(row - 3.5, 2));
        const hotSpotInfluence = Math.sin(now / 10000 + row * col) * 2;
        const loadVariation = Math.sin(now / 5000 + col * 0.5) * 1.5;
        
        const temperature = baseTemp + 
          (distanceFromCenter < 2 ? -1 : distanceFromCenter > 5 ? 2 : 0) +
          hotSpotInfluence + 
          (Math.random() - 0.5) * 1.5;
        
        const power = 60 + loadVariation + (Math.random() - 0.5) * 30;
        
        data.push({
          x: col,
          y: row,
          temperature: Math.max(18, Math.min(32, temperature)),
          power: Math.max(20, Math.min(100, power)),
          id: `R${row + 1}C${col + 1}`,
          status: temperature > 28 ? 'critical' : temperature > 25 ? 'warning' : 'normal'
        });
      }
    }
    
    return data;
  };

  // Generate anomaly data
  const generateAnomalies = () => {
    const anomalyTypes = [
      { icon: 'power', description: 'Unusual power consumption pattern' },
      { icon: 'temperature', description: 'Temperature gradient anomaly' },
      { icon: 'network', description: 'Network bandwidth surge detected' },
      { icon: 'cooling', description: 'HVAC performance degradation' }
    ];
    
    const locations = ['Rack A3-A5', 'Zone B, Rows 3-4', 'Core switches', 'Cooling Unit 1'];
    const numAnomalies = 2 + Math.floor(Math.random() * 3);
    const anomalies = [];
    
    for (let i = 0; i < numAnomalies; i++) {
      const anomaly = anomalyTypes[Math.floor(Math.random() * anomalyTypes.length)];
      const location = locations[Math.floor(Math.random() * locations.length)];
      const confidence = 75 + Math.floor(Math.random() * 25);
      
      anomalies.push({
        id: i + 1,
        ...anomaly,
        location,
        confidence,
        severity: confidence > 90 ? 'critical' : confidence > 80 ? 'warning' : 'info'
      });
    }
    
    return anomalies.sort((a, b) => b.confidence - a.confidence);
  };

  // Generate time series data
  const generateTimeSeriesData = () => {
    const data = [];
    const now = new Date();
    
    for (let i = 23; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 60 * 60 * 1000);
      const hour = time.getHours();
      
      const loadFactor = 0.8 + 0.3 * Math.sin((hour - 6) * Math.PI / 12);
      const coolingFactor = 1 + 0.2 * Math.sin((hour - 14) * Math.PI / 12);
      
      data.push({
        time: time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
        power: 1.42 * loadFactor + (Math.random() - 0.5) * 0.05,
        cooling: 1.1 * coolingFactor + (Math.random() - 0.5) * 0.03,
        temperature: 22.5 + 2 * Math.sin((hour - 14) * Math.PI / 12) + (Math.random() - 0.5) * 0.8,
        effectiveness: 85 + 10 * Math.sin((hour - 10) * Math.PI / 12) + (Math.random() - 0.5) * 2
      });
    }
    
    return data;
  };

  const [timeSeriesData, setTimeSeriesData] = useState(generateTimeSeriesData());

  // Initialize data
  useEffect(() => {
    setAlarms(generateAlarmData());
    setHeatmapData(generateHeatmapData());
    setAnomalies(generateAnomalies());
  }, []);

  // Real-time updates
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
      setKpiData(generateKPIData());
      setTimeSeriesData(generateTimeSeriesData());
    }, 1000);
    
    return () => clearInterval(timer);
  }, []);

  // Update heatmap every 3 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setHeatmapData(generateHeatmapData());
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  // Update alarms every 15 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setAlarms(generateAlarmData());
    }, 15000);
    return () => clearInterval(interval);
  }, []);

  // Update anomalies every 20 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setAnomalies(generateAnomalies());
    }, 20000);
    return () => clearInterval(interval);
  }, []);

  const getTemperatureColor = (temp) => {
    if (temp < 20) return '#4ade80';
    if (temp < 22) return '#84cc16';
    if (temp < 24) return '#eab308';
    if (temp < 26) return '#f97316';
    if (temp < 28) return '#ef4444';
    return '#dc2626';
  };

  const getAlarmColor = (type) => {
    switch (type) {
      case 'Critical': return 'text-red-600 bg-red-50';
      case 'Warning': return 'text-amber-600 bg-amber-50';
      case 'Info': return 'text-blue-600 bg-blue-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const KPIGauge = ({ title, value, unit, max, color = 'blue' }) => {
    const percentage = (value / max) * 100;
    const strokeColor = color === 'green' ? '#00ff88' : color === 'red' ? '#ff0044' : '#00ffff';
    const glowColor = color === 'green' ? 'rgba(0, 255, 136, 0.4)' : color === 'red' ? 'rgba(255, 0, 68, 0.4)' : 'rgba(0, 255, 255, 0.4)';

    return (
      <div className="neon-border p-6 rounded-lg relative overflow-hidden matrix-bg">
        <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
        <div className="text-sm text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase">{title}</div>
        <div className="flex items-center justify-between">
          <div className="text-3xl font-bold hologram-text font-['Orbitron']">
            {value}<span className="text-lg text-cyan-300">{unit}</span>
          </div>
          <div className="w-20 h-20 relative">
            <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
              <defs>
                <filter id={`glow-${title}`}>
                  <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                  <feMerge>
                    <feMergeNode in="coloredBlur"/>
                    <feMergeNode in="SourceGraphic"/>
                  </feMerge>
                </filter>
              </defs>
              <path
                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke="rgba(0, 255, 255, 0.2)"
                strokeWidth="2"
              />
              <path
                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                fill="none"
                stroke={strokeColor}
                strokeWidth="3"
                strokeDasharray={`${percentage}, 100`}
                strokeLinecap="round"
                style={{ filter: `drop-shadow(0 0 10px ${glowColor})` }}
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-sm font-bold hologram-text font-['Orbitron']">{Math.round(percentage)}%</span>
            </div>
          </div>
        </div>
        <div className="mt-2 text-xs text-cyan-500 font-['Exo_2']">
          MAX: {max}{unit}
        </div>
      </div>
    );
  };

  return (
    <div className="futuristic-bg min-h-screen">
      {/* Header */}
      <header className="neon-border backdrop-blur-md px-6 py-4 relative">
        <div className="scan-line"></div>
        <div className="flex items-center justify-between relative z-10">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Menu className="w-8 h-8 text-cyan-400 pulse-glow cursor-pointer hover:text-cyan-300 transition-colors" />
              <div className="absolute inset-0 w-8 h-8 border border-cyan-400 rounded opacity-30 animate-pulse"></div>
            </div>
            <div>
              <h1 className="text-2xl font-bold hologram-text font-['Orbitron']">
                UNIFIED DATA CENTER
              </h1>
              <div className="text-sm text-cyan-300 font-['Exo_2'] tracking-wider">
                NEURAL COMMAND INTERFACE
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-6">
            <div className="relative neon-border rounded-lg px-4 py-2">
              <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-cyan-400" />
              <input
                type="text"
                placeholder="Search neural networks..."
                className="pl-10 pr-4 py-1 bg-transparent text-cyan-300 placeholder-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-400 font-['Exo_2']"
              />
            </div>
            <div className="text-sm text-cyan-300 font-['Exo_2'] font-mono">
              <div className="text-xs text-cyan-500">SYSTEM TIME</div>
              <div className="hologram-text">
                {currentTime.toLocaleTimeString()}
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Bell className="w-6 h-6 text-cyan-400 hover:text-cyan-300 transition-colors cursor-pointer" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              </div>
              <div className="relative">
                <User className="w-6 h-6 text-cyan-400 hover:text-cyan-300 transition-colors cursor-pointer" />
                <div className="absolute inset-0 w-6 h-6 border border-cyan-400 rounded-full animate-ping opacity-20"></div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="p-6 space-y-6 relative">
        {/* Top Row - Executive Overview & Energy Console */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Executive Overview */}
          <div className="neon-border rounded-lg p-6 relative overflow-hidden matrix-bg">
            <div className="scan-line"></div>
            <h2 className="text-xl font-bold mb-6 hologram-text font-['Orbitron'] tracking-wider">
              EXECUTIVE OVERVIEW
            </h2>

            {/* KPI Cards */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <KPIGauge title="PUE" value={kpiData.pue.toFixed(2)} unit="" max={3} color="green" />
              <KPIGauge title="IT Load" value={kpiData.itLoad.toFixed(1)} unit="M" max={2} color="blue" />
              <KPIGauge title="Cooling Load" value={Math.round(kpiData.coolingLoad * 650)} unit=" RT" max={1000} color="blue" />
              <div className="neon-border p-4 rounded-lg relative overflow-hidden matrix-bg">
                <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                <div className="text-sm text-cyan-400 mb-2 font-['Exo_2'] tracking-wider uppercase">Alarms</div>
                <div className="text-2xl font-bold text-red-400 hologram-text font-['Orbitron']">{kpiData.totalAlarms}</div>
                <div className="text-xs text-cyan-500 font-['Exo_2']">{kpiData.activeAlarms} Active</div>
                <div className="absolute top-2 right-2">
                  <AlertTriangle className="w-5 h-5 text-red-400 animate-pulse" />
                </div>
              </div>
            </div>

            {/* Thermal Effectiveness Chart */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase">
                Thermal Effectiveness Matrix
              </h3>
              <div className="h-40 neon-border rounded-lg p-4 relative overflow-hidden">
                <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={timeSeriesData.slice(-12)}>
                    <defs>
                      <linearGradient id="colorEffectiveness" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#00ffff" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#00ffff" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="2 2" stroke="rgba(0, 255, 255, 0.2)" />
                    <XAxis
                      dataKey="time"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}
                    />
                    <YAxis
                      domain={[75, 95]}
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(0, 20, 40, 0.9)',
                        border: '1px solid rgba(0, 255, 255, 0.3)',
                        borderRadius: '8px',
                        color: '#00ffff',
                        fontFamily: 'Exo 2'
                      }}
                    />
                    <Area
                      type="monotone"
                      dataKey="effectiveness"
                      stroke="#00ffff"
                      strokeWidth={2}
                      fillOpacity={1}
                      fill="url(#colorEffectiveness)"
                      style={{ filter: 'drop-shadow(0 0 10px rgba(0, 255, 255, 0.4))' }}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Alarms Table */}
            <div>
              <h3 className="text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase">
                Neural Alert Matrix
              </h3>
              <div className="space-y-2 max-h-40 overflow-y-auto custom-scrollbar">
                {alarms.slice(0, 4).map((alarm) => (
                  <div key={alarm.id} className="neon-border p-3 rounded-lg relative overflow-hidden matrix-bg hover:neon-glow transition-all duration-300">
                    <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-3">
                        <AlertTriangle className={`w-5 h-5 ${alarm.type === 'Critical' ? 'text-red-400' : alarm.type === 'Warning' ? 'text-yellow-400' : 'text-cyan-400'} animate-pulse`} />
                        <div>
                          <div className="font-medium text-sm text-cyan-300 font-['Exo_2']">{alarm.message}</div>
                          <div className="text-xs text-cyan-500 font-['Exo_2']">{alarm.location} • {alarm.status}</div>
                        </div>
                      </div>
                      <div className="text-xs text-cyan-400 font-['Orbitron']">{alarm.time}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Energy Console */}
          <div className="neon-border rounded-lg p-6 relative overflow-hidden matrix-bg">
            <div className="scan-line"></div>
            <h2 className="text-xl font-bold mb-6 hologram-text font-['Orbitron'] tracking-wider">
              ENERGY CONSOLE
            </h2>
            
            {/* Energy Flow Diagram */}
            <div className="mb-6">
              <div className="flex items-center justify-between">
                <div className="text-center">
                  <div className="neon-border p-4 rounded-lg mb-2 relative overflow-hidden matrix-bg">
                    <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                    <div className="text-sm text-cyan-400 font-['Exo_2'] tracking-wider">PUE</div>
                    <div className="text-2xl font-bold hologram-text font-['Orbitron']">{kpiData.pue.toFixed(2)}</div>
                    <Zap className="w-6 h-6 text-cyan-400 mx-auto mt-2 animate-pulse" />
                  </div>
                </div>
                <div className="flex-1 mx-4">
                  <div className="relative">
                    <div className="h-2 bg-gray-800 rounded-full border border-cyan-400"></div>
                    <div
                      className="h-2 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full absolute top-0 left-0 pulse-glow"
                      style={{width: `${Math.min(100, kpiData.pue * 33)}%`}}
                    ></div>
                  </div>
                  <div className="text-center mt-3">
                    <div className="neon-border p-3 rounded-lg relative overflow-hidden matrix-bg">
                      <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                      <div className="text-xs text-cyan-400 font-['Exo_2']">UPS</div>
                      <div className="font-bold text-green-400 hologram-text font-['Orbitron']">{kpiData.upsPower.toFixed(2)}W</div>
                      <Activity className="w-4 h-4 text-green-400 mx-auto mt-1 animate-pulse" />
                    </div>
                  </div>
                </div>
                <div className="text-center">
                  <div className="neon-border p-4 rounded-lg mb-2 relative overflow-hidden matrix-bg">
                    <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                    <div className="text-sm text-cyan-400 font-['Exo_2'] tracking-wider">PDU</div>
                    <div className="text-2xl font-bold hologram-text font-['Orbitron']">{kpiData.pduPower.toFixed(1)}M</div>
                    <HardDrive className="w-6 h-6 text-cyan-400 mx-auto mt-2 animate-pulse" />
                  </div>
                </div>
              </div>
            </div>

            {/* Power Metrics */}
            <div className="grid grid-cols-2 gap-4 mb-6">
              <KPIGauge title="Trial Power" value={kpiData.trialPower.toFixed(1)} unit=" MW" max={5} color="green" />
              <KPIGauge title="Cooling Load" value={kpiData.coolingLoad.toFixed(1)} unit=" MW" max={2} color="blue" />
            </div>

            {/* Power Load Trend */}
            <div>
              <h3 className="text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase">
                Power Load Neural Network
              </h3>
              <div className="h-48 neon-border rounded-lg p-4 relative overflow-hidden">
                <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={timeSeriesData}>
                    <CartesianGrid strokeDasharray="2 2" stroke="rgba(0, 255, 255, 0.2)" />
                    <XAxis
                      dataKey="time"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}
                    />
                    <YAxis
                      domain={[1.3, 1.6]}
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(0, 20, 40, 0.9)',
                        border: '1px solid rgba(0, 255, 255, 0.3)',
                        borderRadius: '8px',
                        color: '#00ffff',
                        fontFamily: 'Exo 2'
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="power"
                      stroke="#00ffff"
                      strokeWidth={3}
                      dot={false}
                      style={{ filter: 'drop-shadow(0 0 10px rgba(0, 255, 255, 0.6))' }}
                    />
                    <Line
                      type="monotone"
                      dataKey="cooling"
                      stroke="#00ff88"
                      strokeWidth={3}
                      dot={false}
                      style={{ filter: 'drop-shadow(0 0 10px rgba(0, 255, 136, 0.6))' }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Row - Asset 360 & Predictive AI Insights */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Asset 360 - Rack Heatmap */}
          <div className="neon-border rounded-lg p-6 relative overflow-hidden matrix-bg">
            <div className="scan-line"></div>
            <h2 className="text-xl font-bold mb-6 hologram-text font-['Orbitron'] tracking-wider">
              ASSET 360° - THERMAL MATRIX
            </h2>
            
            <div className="mb-4">
              <div className="flex justify-between items-center mb-4">
                <span className="text-sm text-cyan-400 font-['Exo_2'] tracking-wider">
                  DATA HALL THERMAL DISTRIBUTION
                </span>
                <div className="flex items-center space-x-3 text-xs">
                  <span className="text-cyan-400 font-['Exo_2']">COOL</span>
                  <div className="flex space-x-1">
                    <div className="w-4 h-4 bg-green-400 rounded border border-green-300 pulse-glow"></div>
                    <div className="w-4 h-4 bg-yellow-400 rounded border border-yellow-300 pulse-glow"></div>
                    <div className="w-4 h-4 bg-orange-400 rounded border border-orange-300 pulse-glow"></div>
                    <div className="w-4 h-4 bg-red-400 rounded border border-red-300 pulse-glow"></div>
                    <div className="w-4 h-4 bg-red-600 rounded border border-red-500 pulse-glow"></div>
                  </div>
                  <span className="text-cyan-400 font-['Exo_2']">HOT</span>
                </div>
              </div>

              {/* Heatmap Grid */}
              <div className="grid grid-cols-10 gap-2 p-6 neon-border rounded-lg relative overflow-hidden matrix-bg">
                <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                {heatmapData.map((rack, index) => (
                  <div
                    key={index}
                    className="aspect-square rounded cursor-pointer hover:scale-125 transition-all duration-300 border-2 border-transparent hover:border-cyan-400 relative"
                    style={{
                      backgroundColor: getTemperatureColor(rack.temperature),
                      boxShadow: `0 0 10px ${getTemperatureColor(rack.temperature)}40`
                    }}
                    onClick={() => setSelectedRack(rack)}
                    title={`${rack.id}: ${rack.temperature.toFixed(1)}°C`}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded"></div>
                  </div>
                ))}
              </div>

              {selectedRack && (
                <div className="mt-4 neon-border rounded-lg p-4 relative overflow-hidden matrix-bg">
                  <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                  <div className="flex items-center space-x-3 mb-3">
                    <Server className="w-6 h-6 text-cyan-400 animate-pulse" />
                    <div className="font-bold text-cyan-300 font-['Orbitron'] text-lg">
                      RACK {selectedRack.id}
                    </div>
                  </div>
                  <div className="text-sm text-cyan-300 space-y-2 font-['Exo_2']">
                    <div className="flex justify-between">
                      <span>Temperature:</span>
                      <span className="font-bold hologram-text">{selectedRack.temperature.toFixed(1)}°C</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Power Load:</span>
                      <span className="font-bold hologram-text">{selectedRack.power.toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <span className={`font-bold ${selectedRack.status === 'critical' ? 'text-red-400' : selectedRack.status === 'warning' ? 'text-yellow-400' : 'text-green-400'} hologram-text`}>
                        {selectedRack.status.toUpperCase()}
                      </span>
                    </div>
                    <div className="text-xs text-cyan-500 mt-3 border-t border-cyan-800 pt-2">
                      Last updated: {currentTime.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Capacity Forecast */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase">
                Capacity Neural Forecast
              </h3>
              <div className="h-40 neon-border rounded-lg p-4 relative overflow-hidden">
                <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={timeSeriesData}>
                    <CartesianGrid strokeDasharray="2 2" stroke="rgba(0, 255, 255, 0.2)" />
                    <XAxis
                      dataKey="time"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(0, 20, 40, 0.9)',
                        border: '1px solid rgba(0, 255, 255, 0.3)',
                        borderRadius: '8px',
                        color: '#00ffff',
                        fontFamily: 'Exo 2'
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="power"
                      stroke="#8b5cf6"
                      strokeWidth={3}
                      dot={false}
                      style={{ filter: 'drop-shadow(0 0 10px rgba(139, 92, 246, 0.6))' }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Power Consumption */}
            <div>
              <h3 className="text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase">
                Power Consumption Matrix
              </h3>
              <div className="h-40 neon-border rounded-lg p-4 relative overflow-hidden">
                <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={timeSeriesData}>
                    <defs>
                      <linearGradient id="colorPower" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#00ff88" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#00ff88" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="2 2" stroke="rgba(0, 255, 255, 0.2)" />
                    <XAxis
                      dataKey="time"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(0, 20, 40, 0.9)',
                        border: '1px solid rgba(0, 255, 255, 0.3)',
                        borderRadius: '8px',
                        color: '#00ffff',
                        fontFamily: 'Exo 2'
                      }}
                    />
                    <Area
                      type="monotone"
                      dataKey="cooling"
                      stroke="#00ff88"
                      strokeWidth={2}
                      fillOpacity={1}
                      fill="url(#colorPower)"
                      style={{ filter: 'drop-shadow(0 0 10px rgba(0, 255, 136, 0.4))' }}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>

          {/* Predictive AI Insights */}
          <div className="neon-border rounded-lg p-6 relative overflow-hidden matrix-bg">
            <div className="scan-line"></div>
            <h2 className="text-xl font-bold mb-6 hologram-text font-['Orbitron'] tracking-wider">
              PREDICTIVE AI INSIGHTS
            </h2>
            
            {/* Cooling Forecast */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase">
                Thermal Neural Forecast
              </h3>
              <div className="h-40 mb-4 neon-border rounded-lg p-4 relative overflow-hidden">
                <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={timeSeriesData}>
                    <CartesianGrid strokeDasharray="2 2" stroke="rgba(0, 255, 255, 0.2)" />
                    <XAxis
                      dataKey="time"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}
                    />
                    <YAxis
                      domain={[20, 26]}
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#00ffff', fontSize: 10, fontFamily: 'Exo 2' }}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(0, 20, 40, 0.9)',
                        border: '1px solid rgba(0, 255, 255, 0.3)',
                        borderRadius: '8px',
                        color: '#00ffff',
                        fontFamily: 'Exo 2'
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="temperature"
                      stroke="#f59e0b"
                      strokeWidth={3}
                      dot={false}
                      style={{ filter: 'drop-shadow(0 0 10px rgba(245, 158, 11, 0.6))' }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>

              {/* Recommendations */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-cyan-400 font-['Exo_2'] tracking-wider uppercase">
                  AI Recommendations
                </h4>
                <div className="space-y-3 text-sm">
                  <div className="neon-border p-3 rounded-lg relative overflow-hidden matrix-bg hover:neon-glow transition-all duration-300">
                    <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                    <div className="flex justify-between items-center">
                      <span className="text-cyan-300 font-['Exo_2']">Optimize I/O airflows Sector A</span>
                      <span className="text-green-400 font-['Orbitron'] font-bold">EXEC A</span>
                    </div>
                  </div>
                  <div className="neon-border p-3 rounded-lg relative overflow-hidden matrix-bg hover:neon-glow transition-all duration-300">
                    <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                    <div className="flex justify-between items-center">
                      <span className="text-cyan-300 font-['Exo_2']">Monitor return chilled water</span>
                      <span className="text-yellow-400 font-['Orbitron'] font-bold">CHECK B</span>
                    </div>
                  </div>
                  <div className="neon-border p-3 rounded-lg relative overflow-hidden matrix-bg hover:neon-glow transition-all duration-300">
                    <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                    <div className="flex justify-between items-center">
                      <span className="text-cyan-300 font-['Exo_2']">Server space cooling pump</span>
                      <span className="text-blue-400 font-['Orbitron'] font-bold">CHECK A</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Anomaly Detection */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase">
                Neural Anomaly Detection
              </h3>
              <div className="space-y-3">
                {anomalies.map((anomaly) => {
                  const textColor = anomaly.severity === 'critical' ? 'text-red-400' :
                                   anomaly.severity === 'warning' ? 'text-yellow-400' : 'text-blue-400';

                  return (
                    <div key={anomaly.id} className="neon-border p-4 rounded-lg relative overflow-hidden matrix-bg hover:neon-glow transition-all duration-300">
                      <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          {anomaly.icon === 'power' && <Zap className={`w-6 h-6 ${textColor} animate-pulse`} />}
                          {anomaly.icon === 'temperature' && <Thermometer className={`w-6 h-6 ${textColor} animate-pulse`} />}
                          {anomaly.icon === 'network' && <Wifi className={`w-6 h-6 ${textColor} animate-pulse`} />}
                          {anomaly.icon === 'cooling' && <Server className={`w-6 h-6 ${textColor} animate-pulse`} />}
                          <div>
                            <div className="font-medium text-sm text-cyan-300 font-['Exo_2']">{anomaly.description}</div>
                            <div className="text-xs text-cyan-500 font-['Exo_2']">{anomaly.location}</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold hologram-text font-['Orbitron']">{anomaly.confidence}%</div>
                          <div className="text-xs text-cyan-400 font-['Exo_2'] tracking-wider">CONFIDENCE</div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Additional Recommendations */}
            <div>
              <h3 className="text-sm font-medium text-cyan-400 mb-3 font-['Exo_2'] tracking-wider uppercase">
                System Neural Recommendations
              </h3>
              <div className="space-y-3 text-sm">
                <div className="neon-border p-3 rounded-lg relative overflow-hidden matrix-bg hover:neon-glow transition-all duration-300">
                  <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                  <div className="flex justify-between items-center">
                    <span className="text-cyan-300 font-['Exo_2']">Optimize CRAC parameters Sector A</span>
                    <div className="w-3 h-3 bg-purple-400 rounded-full animate-pulse pulse-glow"></div>
                  </div>
                </div>
                <div className="neon-border p-3 rounded-lg relative overflow-hidden matrix-bg hover:neon-glow transition-all duration-300">
                  <div className="data-stream absolute top-0 left-0 w-full h-1"></div>
                  <div className="flex justify-between items-center">
                    <span className="text-cyan-300 font-['Exo_2']">Investigate thermal sensors Rack M</span>
                    <div className="w-3 h-3 bg-indigo-400 rounded-full animate-pulse pulse-glow"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Status Footer */}
        <div className="neon-border rounded-lg p-6 relative overflow-hidden matrix-bg">
          <div className="scan-line"></div>
          <div className="flex justify-between items-center text-sm relative z-10">
            <div className="text-cyan-400 font-['Exo_2']">
              <span className="text-xs tracking-wider uppercase">Last Neural Sync:</span>
              <div className="hologram-text font-['Orbitron'] text-lg">{currentTime.toLocaleTimeString()}</div>
            </div>
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-3">
                <div className="w-4 h-4 bg-green-400 rounded-full animate-pulse pulse-glow"></div>
                <span className="text-cyan-300 font-['Exo_2'] tracking-wider">ALL SYSTEMS OPERATIONAL</span>
              </div>
              <div className="text-cyan-400 font-['Exo_2']">
                <span className="text-xs tracking-wider uppercase">Data Stream:</span>
                <div className="hologram-text font-['Orbitron']">LIVE</div>
              </div>
              <div className="flex items-center space-x-3">
                <Cpu className="w-5 h-5 text-cyan-400 animate-pulse" />
                <span className="text-cyan-300 font-['Exo_2']">
                  <span className="text-xs tracking-wider uppercase">Neural Nodes:</span>
                  <div className="hologram-text font-['Orbitron']">{heatmapData.length}</div>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnifiedDataCenterDashboard;