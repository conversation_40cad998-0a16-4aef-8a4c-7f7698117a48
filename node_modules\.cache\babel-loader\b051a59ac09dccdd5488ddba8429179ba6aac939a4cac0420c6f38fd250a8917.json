{"ast": null, "code": "var _jsxFileName = \"D:\\\\00-WKYap\\\\12-AIApps-AgumentCode\\\\01-Unified IPS Dashboard\\\\src\\\\components\\\\ThemeSelector.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport UnifiedDataCenterDashboard from './UnifiedDataCenterDashboard.tsx';\nimport GlassmorphismDataCenterDashboard from './GlassmorphismDataCenterDashboard.tsx';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeSelector = () => {\n  _s();\n  var _themes$find, _themes$find2;\n  const [currentTheme, setCurrentTheme] = useState('futuristic');\n  const themes = [{\n    id: 'futuristic',\n    name: 'Futuristic Neon',\n    description: 'Cyberpunk-inspired theme with neon colors and matrix effects',\n    preview: 'linear-gradient(135deg, #001122 0%, #003366 100%)',\n    component: UnifiedDataCenterDashboard\n  }, {\n    id: 'glassmorphism',\n    name: 'Glassmorphism',\n    description: 'Modern glass-like transparent design with blur effects',\n    preview: 'linear-gradient(135deg, rgba(100, 116, 139, 0.4) 0%, rgba(15, 23, 42, 0.4) 100%)',\n    component: GlassmorphismDataCenterDashboard\n  }];\n  const CurrentComponent = ((_themes$find = themes.find(theme => theme.id === currentTheme)) === null || _themes$find === void 0 ? void 0 : _themes$find.component) || UnifiedDataCenterDashboard;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-4 left-4 z-50 flex space-x-2\",\n      children: themes.map(theme => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setCurrentTheme(theme.id),\n        className: `px-4 py-2 rounded-lg backdrop-blur-md border transition-all duration-300 ${currentTheme === theme.id ? 'bg-white bg-opacity-20 border-white border-opacity-40 text-white shadow-lg' : 'bg-black bg-opacity-20 border-white border-opacity-20 text-white text-opacity-70 hover:bg-opacity-30'}`,\n        style: {\n          backdropFilter: 'blur(10px)',\n          WebkitBackdropFilter: 'blur(10px)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-4 h-4 rounded-full border border-white border-opacity-30\",\n            style: {\n              background: theme.preview\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium text-sm\",\n            children: theme.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 13\n        }, this)\n      }, theme.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-16 left-4 z-40 max-w-xs\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-3 py-2 rounded-lg backdrop-blur-md bg-black bg-opacity-20 border border-white border-opacity-20 text-white text-opacity-80\",\n        style: {\n          backdropFilter: 'blur(10px)',\n          WebkitBackdropFilter: 'blur(10px)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs\",\n          children: (_themes$find2 = themes.find(theme => theme.id === currentTheme)) === null || _themes$find2 === void 0 ? void 0 : _themes$find2.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), currentTheme === 'glassmorphism' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0\",\n        style: {\n          backgroundImage: `url(\"https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\")`,\n          backgroundSize: 'cover',\n          backgroundPosition: 'center',\n          backgroundRepeat: 'no-repeat',\n          filter: 'brightness(0.3) contrast(1.1)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0\",\n        style: {\n          background: `\n                linear-gradient(135deg,\n                  rgba(15, 23, 42, 0.7) 0%,\n                  rgba(30, 41, 59, 0.6) 25%,\n                  rgba(51, 65, 85, 0.5) 50%,\n                  rgba(71, 85, 105, 0.4) 75%,\n                  rgba(100, 116, 139, 0.3) 100%)\n              `\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 opacity-20\",\n        style: {\n          backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0\",\n        style: {\n          backdropFilter: 'blur(1px)',\n          WebkitBackdropFilter: 'blur(1px)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10\",\n      children: /*#__PURE__*/_jsxDEV(CurrentComponent, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_s(ThemeSelector, \"8f9xcVuVgwIdgmCxdmv02Lnyy40=\");\n_c = ThemeSelector;\nexport default ThemeSelector;\nvar _c;\n$RefreshReg$(_c, \"ThemeSelector\");", "map": {"version": 3, "names": ["React", "useState", "UnifiedDataCenterDashboard", "GlassmorphismDataCenterDashboard", "jsxDEV", "_jsxDEV", "ThemeSelector", "_s", "_themes$find", "_themes$find2", "currentTheme", "setCurrentTheme", "themes", "id", "name", "description", "preview", "component", "CurrentComponent", "find", "theme", "className", "children", "map", "onClick", "style", "<PERSON><PERSON>ilter", "WebkitBackdropFilter", "background", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "backgroundImage", "backgroundSize", "backgroundPosition", "backgroundRepeat", "filter", "_c", "$RefreshReg$"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/src/components/ThemeSelector.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport UnifiedDataCenterDashboard from './UnifiedDataCenterDashboard.tsx';\nimport GlassmorphismDataCenterDashboard from './GlassmorphismDataCenterDashboard.tsx';\n\nconst ThemeSelector = () => {\n  const [currentTheme, setCurrentTheme] = useState<'futuristic' | 'glassmorphism'>('futuristic');\n\n  const themes = [\n    {\n      id: 'futuristic',\n      name: 'Futuristic Neon',\n      description: 'Cyberpunk-inspired theme with neon colors and matrix effects',\n      preview: 'linear-gradient(135deg, #001122 0%, #003366 100%)',\n      component: UnifiedDataCenterDashboard\n    },\n    {\n      id: 'glassmorphism',\n      name: 'Glassmorphism',\n      description: 'Modern glass-like transparent design with blur effects',\n      preview: 'linear-gradient(135deg, rgba(100, 116, 139, 0.4) 0%, rgba(15, 23, 42, 0.4) 100%)',\n      component: GlassmorphismDataCenterDashboard\n    }\n  ];\n\n  const CurrentComponent = themes.find(theme => theme.id === currentTheme)?.component || UnifiedDataCenterDashboard;\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Theme Selector Header */}\n      <div className=\"fixed top-4 left-4 z-50 flex space-x-2\">\n        {themes.map((theme) => (\n          <button\n            key={theme.id}\n            onClick={() => setCurrentTheme(theme.id as 'futuristic' | 'glassmorphism')}\n            className={`px-4 py-2 rounded-lg backdrop-blur-md border transition-all duration-300 ${\n              currentTheme === theme.id\n                ? 'bg-white bg-opacity-20 border-white border-opacity-40 text-white shadow-lg'\n                : 'bg-black bg-opacity-20 border-white border-opacity-20 text-white text-opacity-70 hover:bg-opacity-30'\n            }`}\n            style={{\n              backdropFilter: 'blur(10px)',\n              WebkitBackdropFilter: 'blur(10px)'\n            }}\n          >\n            <div className=\"flex items-center space-x-2\">\n              <div \n                className=\"w-4 h-4 rounded-full border border-white border-opacity-30\"\n                style={{ background: theme.preview }}\n              ></div>\n              <span className=\"font-medium text-sm\">{theme.name}</span>\n            </div>\n          </button>\n        ))}\n      </div>\n\n      {/* Theme Description */}\n      <div className=\"fixed top-16 left-4 z-40 max-w-xs\">\n        <div \n          className=\"px-3 py-2 rounded-lg backdrop-blur-md bg-black bg-opacity-20 border border-white border-opacity-20 text-white text-opacity-80\"\n          style={{\n            backdropFilter: 'blur(10px)',\n            WebkitBackdropFilter: 'blur(10px)'\n          }}\n        >\n          <p className=\"text-xs\">\n            {themes.find(theme => theme.id === currentTheme)?.description}\n          </p>\n        </div>\n      </div>\n\n      {/* Background for Glassmorphism Theme */}\n      {currentTheme === 'glassmorphism' && (\n        <div className=\"fixed inset-0 z-0\">\n          {/* Building Background Image */}\n          <div\n            className=\"absolute inset-0\"\n            style={{\n              backgroundImage: `url(\"https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\")`,\n              backgroundSize: 'cover',\n              backgroundPosition: 'center',\n              backgroundRepeat: 'no-repeat',\n              filter: 'brightness(0.3) contrast(1.1)'\n            }}\n          />\n\n          {/* Gradient Overlay */}\n          <div\n            className=\"absolute inset-0\"\n            style={{\n              background: `\n                linear-gradient(135deg,\n                  rgba(15, 23, 42, 0.7) 0%,\n                  rgba(30, 41, 59, 0.6) 25%,\n                  rgba(51, 65, 85, 0.5) 50%,\n                  rgba(71, 85, 105, 0.4) 75%,\n                  rgba(100, 116, 139, 0.3) 100%)\n              `\n            }}\n          />\n\n          {/* Subtle Pattern Overlay */}\n          <div\n            className=\"absolute inset-0 opacity-20\"\n            style={{\n              backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`\n            }}\n          />\n\n          {/* Additional Glass Effect */}\n          <div\n            className=\"absolute inset-0\"\n            style={{\n              backdropFilter: 'blur(1px)',\n              WebkitBackdropFilter: 'blur(1px)'\n            }}\n          />\n        </div>\n      )}\n\n      {/* Dashboard Component */}\n      <div className=\"relative z-10\">\n        <CurrentComponent />\n      </div>\n    </div>\n  );\n};\n\nexport default ThemeSelector;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,0BAA0B,MAAM,kCAAkC;AACzE,OAAOC,gCAAgC,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtF,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,aAAA;EAC1B,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAiC,YAAY,CAAC;EAE9F,MAAMW,MAAM,GAAG,CACb;IACEC,EAAE,EAAE,YAAY;IAChBC,IAAI,EAAE,iBAAiB;IACvBC,WAAW,EAAE,8DAA8D;IAC3EC,OAAO,EAAE,mDAAmD;IAC5DC,SAAS,EAAEf;EACb,CAAC,EACD;IACEW,EAAE,EAAE,eAAe;IACnBC,IAAI,EAAE,eAAe;IACrBC,WAAW,EAAE,wDAAwD;IACrEC,OAAO,EAAE,kFAAkF;IAC3FC,SAAS,EAAEd;EACb,CAAC,CACF;EAED,MAAMe,gBAAgB,GAAG,EAAAV,YAAA,GAAAI,MAAM,CAACO,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACP,EAAE,KAAKH,YAAY,CAAC,cAAAF,YAAA,uBAA/CA,YAAA,CAAiDS,SAAS,KAAIf,0BAA0B;EAEjH,oBACEG,OAAA;IAAKgB,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAE3BjB,OAAA;MAAKgB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,EACpDV,MAAM,CAACW,GAAG,CAAEH,KAAK,iBAChBf,OAAA;QAEEmB,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAACS,KAAK,CAACP,EAAoC,CAAE;QAC3EQ,SAAS,EAAE,4EACTX,YAAY,KAAKU,KAAK,CAACP,EAAE,GACrB,4EAA4E,GAC5E,sGAAsG,EACzG;QACHY,KAAK,EAAE;UACLC,cAAc,EAAE,YAAY;UAC5BC,oBAAoB,EAAE;QACxB,CAAE;QAAAL,QAAA,eAEFjB,OAAA;UAAKgB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CjB,OAAA;YACEgB,SAAS,EAAC,4DAA4D;YACtEI,KAAK,EAAE;cAAEG,UAAU,EAAER,KAAK,CAACJ;YAAQ;UAAE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACP3B,OAAA;YAAMgB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAEF,KAAK,CAACN;UAAI;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD;MAAC,GAlBDZ,KAAK,CAACP,EAAE;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmBP,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN3B,OAAA;MAAKgB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAChDjB,OAAA;QACEgB,SAAS,EAAC,+HAA+H;QACzII,KAAK,EAAE;UACLC,cAAc,EAAE,YAAY;UAC5BC,oBAAoB,EAAE;QACxB,CAAE;QAAAL,QAAA,eAEFjB,OAAA;UAAGgB,SAAS,EAAC,SAAS;UAAAC,QAAA,GAAAb,aAAA,GACnBG,MAAM,CAACO,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACP,EAAE,KAAKH,YAAY,CAAC,cAAAD,aAAA,uBAA/CA,aAAA,CAAiDM;QAAW;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLtB,YAAY,KAAK,eAAe,iBAC/BL,OAAA;MAAKgB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAEhCjB,OAAA;QACEgB,SAAS,EAAC,kBAAkB;QAC5BI,KAAK,EAAE;UACLQ,eAAe,EAAE,+KAA+K;UAChMC,cAAc,EAAE,OAAO;UACvBC,kBAAkB,EAAE,QAAQ;UAC5BC,gBAAgB,EAAE,WAAW;UAC7BC,MAAM,EAAE;QACV;MAAE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGF3B,OAAA;QACEgB,SAAS,EAAC,kBAAkB;QAC5BI,KAAK,EAAE;UACLG,UAAU,EAAE;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;QACY;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGF3B,OAAA;QACEgB,SAAS,EAAC,6BAA6B;QACvCI,KAAK,EAAE;UACLQ,eAAe,EAAE;QACnB;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGF3B,OAAA;QACEgB,SAAS,EAAC,kBAAkB;QAC5BI,KAAK,EAAE;UACLC,cAAc,EAAE,WAAW;UAC3BC,oBAAoB,EAAE;QACxB;MAAE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,eAGD3B,OAAA;MAAKgB,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BjB,OAAA,CAACa,gBAAgB;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CAzHID,aAAa;AAAAgC,EAAA,GAAbhC,aAAa;AA2HnB,eAAeA,aAAa;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}