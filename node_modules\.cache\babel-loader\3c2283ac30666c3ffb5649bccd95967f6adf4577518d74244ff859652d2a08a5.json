{"ast": null, "code": "var baseTrim = require('./_baseTrim'),\n  isObject = require('./isObject'),\n  isSymbol = require('./isSymbol');\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? other + '' : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = baseTrim(value);\n  var isBinary = reIsBinary.test(value);\n  return isBinary || reIsOctal.test(value) ? freeParseInt(value.slice(2), isBinary ? 2 : 8) : reIsBadHex.test(value) ? NAN : +value;\n}\nmodule.exports = toNumber;", "map": {"version": 3, "names": ["baseTrim", "require", "isObject", "isSymbol", "NAN", "reIsBadHex", "reIsBinary", "reIsOctal", "freeParseInt", "parseInt", "toNumber", "value", "other", "valueOf", "isBinary", "test", "slice", "module", "exports"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/node_modules/lodash/toNumber.js"], "sourcesContent": ["var baseTrim = require('./_baseTrim'),\n    isObject = require('./isObject'),\n    isSymbol = require('./isSymbol');\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = baseTrim(value);\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nmodule.exports = toNumber;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;EACjCC,QAAQ,GAAGD,OAAO,CAAC,YAAY,CAAC;EAChCE,QAAQ,GAAGF,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA,IAAIG,GAAG,GAAG,CAAC,GAAG,CAAC;;AAEf;AACA,IAAIC,UAAU,GAAG,oBAAoB;;AAErC;AACA,IAAIC,UAAU,GAAG,YAAY;;AAE7B;AACA,IAAIC,SAAS,GAAG,aAAa;;AAE7B;AACA,IAAIC,YAAY,GAAGC,QAAQ;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,IAAI,OAAOA,KAAK,IAAI,QAAQ,EAAE;IAC5B,OAAOA,KAAK;EACd;EACA,IAAIR,QAAQ,CAACQ,KAAK,CAAC,EAAE;IACnB,OAAOP,GAAG;EACZ;EACA,IAAIF,QAAQ,CAACS,KAAK,CAAC,EAAE;IACnB,IAAIC,KAAK,GAAG,OAAOD,KAAK,CAACE,OAAO,IAAI,UAAU,GAAGF,KAAK,CAACE,OAAO,CAAC,CAAC,GAAGF,KAAK;IACxEA,KAAK,GAAGT,QAAQ,CAACU,KAAK,CAAC,GAAIA,KAAK,GAAG,EAAE,GAAIA,KAAK;EAChD;EACA,IAAI,OAAOD,KAAK,IAAI,QAAQ,EAAE;IAC5B,OAAOA,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK;EACrC;EACAA,KAAK,GAAGX,QAAQ,CAACW,KAAK,CAAC;EACvB,IAAIG,QAAQ,GAAGR,UAAU,CAACS,IAAI,CAACJ,KAAK,CAAC;EACrC,OAAQG,QAAQ,IAAIP,SAAS,CAACQ,IAAI,CAACJ,KAAK,CAAC,GACrCH,YAAY,CAACG,KAAK,CAACK,KAAK,CAAC,CAAC,CAAC,EAAEF,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,GAC7CT,UAAU,CAACU,IAAI,CAACJ,KAAK,CAAC,GAAGP,GAAG,GAAG,CAACO,KAAM;AAC7C;AAEAM,MAAM,CAACC,OAAO,GAAGR,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}