{"ast": null, "code": "var baseIsTypedArray = require('./_baseIsTypedArray'),\n  baseUnary = require('./_baseUnary'),\n  nodeUtil = require('./_nodeUtil');\n\n/* Node.js helper references. */\nvar nodeIsTypedArray = nodeUtil && nodeUtil.isTypedArray;\n\n/**\n * Checks if `value` is classified as a typed array.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\n * @example\n *\n * _.isTypedArray(new Uint8Array);\n * // => true\n *\n * _.isTypedArray([]);\n * // => false\n */\nvar isTypedArray = nodeIsTypedArray ? baseUnary(nodeIsTypedArray) : baseIsTypedArray;\nmodule.exports = isTypedArray;", "map": {"version": 3, "names": ["baseIsTypedArray", "require", "baseUnary", "nodeUtil", "nodeIsTypedArray", "isTypedArray", "module", "exports"], "sources": ["D:/00-WKYap/12-AIApps-AgumentCode/01-Unified IPS Dashboard/node_modules/lodash/isTypedArray.js"], "sourcesContent": ["var baseIsTypedArray = require('./_baseIsTypedArray'),\n    baseUnary = require('./_baseUnary'),\n    nodeUtil = require('./_nodeUtil');\n\n/* Node.js helper references. */\nvar nodeIsTypedArray = nodeUtil && nodeUtil.isTypedArray;\n\n/**\n * Checks if `value` is classified as a typed array.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\n * @example\n *\n * _.isTypedArray(new Uint8Array);\n * // => true\n *\n * _.isTypedArray([]);\n * // => false\n */\nvar isTypedArray = nodeIsTypedArray ? baseUnary(nodeIsTypedArray) : baseIsTypedArray;\n\nmodule.exports = isTypedArray;\n"], "mappings": "AAAA,IAAIA,gBAAgB,GAAGC,OAAO,CAAC,qBAAqB,CAAC;EACjDC,SAAS,GAAGD,OAAO,CAAC,cAAc,CAAC;EACnCE,QAAQ,GAAGF,OAAO,CAAC,aAAa,CAAC;;AAErC;AACA,IAAIG,gBAAgB,GAAGD,QAAQ,IAAIA,QAAQ,CAACE,YAAY;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,YAAY,GAAGD,gBAAgB,GAAGF,SAAS,CAACE,gBAAgB,CAAC,GAAGJ,gBAAgB;AAEpFM,MAAM,CAACC,OAAO,GAAGF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}